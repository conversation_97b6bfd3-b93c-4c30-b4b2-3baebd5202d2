/* AppCove Profile Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
    color: #374151;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    padding: 50px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

/* Header Section */
.header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 60px;
    gap: 50px;
}

.profile-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 280px;
}

.profile-image {
    width: 200px;
    height: 200px;
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
}

.profile-avatar {
    width: 120px;
    height: 120px;
    background-color: #10b981;
    border-radius: 50%;
    position: relative;
}

.avatar-head {
    width: 40px;
    height: 40px;
    background-color: #f3f4f6;
    border-radius: 50%;
    position: absolute;
    top: 25px;
    left: 50%;
    transform: translateX(-50%);
}

.avatar-body {
    width: 60px;
    height: 45px;
    background-color: #f3f4f6;
    border-radius: 30px 30px 0 0;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
}

/* Company Details */
.company-details {
    text-align: left;
    width: 100%;
    background-color: #f9fafb;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #10b981;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    font-size: 14px;
    align-items: center;
}

.detail-row:last-child {
    margin-bottom: 0;
}

.detail-label {
    color: #10b981;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 12px;
}

.detail-value {
    color: #374151;
    font-weight: 500;
    text-align: right;
}

/* Company Info */
.company-info {
    flex: 1;
}

.company-name {
    font-size: 64px;
    font-weight: 300;
    color: #1f2937;
    margin: 0 0 25px 0;
    letter-spacing: -1px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tagline {
    font-size: 22px;
    color: #10b981;
    font-weight: 600;
    margin-bottom: 25px;
    line-height: 1.4;
}

.description {
    font-size: 16px;
    color: #374151;
    line-height: 1.7;
    margin-bottom: 25px;
}

.description.highlight {
    font-weight: 600;
    color: #1f2937;
    background-color: #f0f9ff;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #3b82f6;
}

/* Timeline Section */
.timeline {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    padding: 40px;
    border-radius: 12px;
    margin-bottom: 50px;
    box-shadow: 0 4px 15px rgba(254, 243, 199, 0.5);
}

.timeline h2 {
    color: #1f2937;
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 8px 0;
}

.timeline-subtitle {
    color: #1f2937;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 30px;
    letter-spacing: 1px;
    opacity: 0.8;
}

.timeline-item {
    display: flex;
    margin-bottom: 25px;
    align-items: flex-start;
    position: relative;
}

.timeline-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 25px;
    width: 2px;
    height: calc(100% + 10px);
    background: linear-gradient(to bottom, #10b981, transparent);
    opacity: 0.3;
}

.timeline-dot {
    width: 14px;
    height: 14px;
    background-color: #10b981;
    border-radius: 50%;
    margin-right: 20px;
    margin-top: 6px;
    flex-shrink: 0;
    border: 3px solid white;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4);
    z-index: 1;
    position: relative;
}

.timeline-content h3 {
    margin: 0 0 5px 0;
    font-size: 18px;
    color: #1f2937;
    font-weight: 600;
}

.timeline-years {
    color: #10b981;
    font-weight: 700;
    font-size: 14px;
    margin-bottom: 5px;
    display: inline-block;
    background-color: rgba(16, 185, 129, 0.1);
    padding: 2px 8px;
    border-radius: 4px;
}

.timeline-description {
    color: #4b5563;
    font-size: 15px;
    line-height: 1.6;
}

/* Problems & Answers Section */
.problems-answers {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.section h2 {
    color: #1f2937;
    font-size: 36px;
    font-weight: 600;
    margin: 0 0 30px 0;
    border-bottom: 3px dotted #d1d5db;
    padding-bottom: 15px;
    position: relative;
}

.section h2::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 60px;
    height: 3px;
    background-color: #10b981;
    border-radius: 2px;
}

.problem-item, .answer-item {
    margin-bottom: 35px;
    display: flex;
    align-items: flex-start;
    padding: 20px;
    border-radius: 8px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.problem-item {
    background-color: #fef2f2;
    border-left: 4px solid #ef4444;
}

.answer-item {
    background-color: #f0fdf4;
    border-left: 4px solid #10b981;
}

.problem-item:hover, .answer-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.arrow {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    font-weight: bold;
    margin-right: 20px;
    flex-shrink: 0;
    margin-top: 5px;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    transition: transform 0.2s ease;
}

.arrow:hover {
    transform: scale(1.1);
}

.item-content {
    flex: 1;
}

.item-content p {
    color: #374151;
    line-height: 1.7;
    margin: 0;
    font-size: 15px;
}

/* Image Styles */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

.profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.company-logo {
    max-height: 60px;
    width: auto;
    margin-bottom: 20px;
}

.project-image {
    width: 100%;
    max-width: 300px;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    margin-bottom: 15px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.project-image:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 30px 20px;
    }
    
    .header {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 30px;
    }
    
    .company-name {
        font-size: 48px;
    }
    
    .problems-answers {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .timeline {
        padding: 30px 20px;
    }
    
    .profile-section {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    body {
        padding: 10px;
    }
    
    .container {
        padding: 20px 15px;
    }
    
    .company-name {
        font-size: 36px;
    }
    
    .tagline {
        font-size: 18px;
    }
    
    .profile-image {
        width: 150px;
        height: 150px;
    }
    
    .profile-avatar {
        width: 90px;
        height: 90px;
    }
    
    .avatar-head {
        width: 30px;
        height: 30px;
        top: 20px;
    }
    
    .avatar-body {
        width: 45px;
        height: 35px;
    }
}
