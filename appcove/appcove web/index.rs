#[approck::http(GET /; AUTH None; return HTML;)]
pub mod page {
    use maud::{html, Markup};
    
    pub async fn request(doc: Document) -> Response {
        doc.set_title("AppCove Profile");
        
        // Add CSS link
        doc.add_head(html! {
            link rel="stylesheet" href="styles.css";
        });

        doc.add_body(render_page_content());
        
        Response::HTML(doc.into())
    }

    fn render_page_content() -> Markup {
        html! {
            main.container {
                (render_header_section())
                (render_timeline_section())
                (render_problems_solutions_section())
            }
        }
    }

    fn render_header_section() -> Markup {
        html! {
            header.header {
                (render_profile_section())
                (render_company_info())
            }
        }
    }

    fn render_profile_section() -> Markup {
        html! {
            section.profile-section {
                figure.profile-image {
                    img.profile-img 
                        src="src/images/appcove-profile.png" 
                        alt="AppCove Profile" 
                        loading="lazy";
                }
                dl.company-details {
                    div.detail-row {
                        dt.detail-label { "Founded" }
                        dd.detail-value { "2003" }
                    }
                    div.detail-row {
                        dt.detail-label { "Owner" }
                        dd.detail-value { "Jason Garber" }
                    }
                    div.detail-row {
                        dt.detail-label { "Size" }
                        dd.detail-value { "20+ Employees" }
                    }
                    div.detail-row {
                        dt.detail-label { "Headquarters" }
                        dd.detail-value { "Altoona, PA" }
                    }
                }
            }
        }
    }

    fn render_company_info() -> Markup {
        html! {
            section.company-info {
                figure {
                    img.company-logo 
                        src="src/images/appcove.png" 
                        alt="AppCove Logo" 
                        loading="lazy";
                }
                h1.company-name { "AppCove" }
                p.tagline { 
                    "We build custom software focused on providing your customers with a premium experience." 
                }
                p.description.highlight {
                    "AppCove has a proven track record of building and supporting reliable web applications for over 20 years. We take your long term business goals seriously."
                }
                p.description {
                    "This is reflected in every aspect of our work — from our people and training to our software and contracts. While you focus on innovation and operations, we focus on delivering technical excellence, robust security, and flexible infrastructure — whether hosted with us or deployed on your cloud."
                }
            }
        }
    }

    fn render_timeline_section() -> Markup {
        html! {
            section.timeline {
                header {
                    h2 { "Custom Software Deployment Timeline" }
                    p.timeline-subtitle { "A SELECTION OF NOTABLE PROJECTS" }
                }
                (render_timeline_items())
            }
        }
    }

    fn render_timeline_items() -> Markup {
        let projects = get_timeline_projects();
        
        html! {
            @for project in projects {
                article.timeline-item {
                    div.timeline-dot {}
                    div.timeline-content {
                        time.timeline-years { (project.years) }
                        h3 { (project.title) }
                        p.timeline-description { (project.description) }
                    }
                }
            }
        }
    }

    fn render_problems_solutions_section() -> Markup {
        html! {
            section.problems-answers {
                (render_problems_section())
                (render_solutions_section())
            }
        }
    }

    fn render_problems_section() -> Markup {
        let problems = get_business_problems();
        
        html! {
            div.section {
                h2 { "Problems" }
                @for problem in problems {
                    div.problem-item {
                        div.item-content {
                            p { (problem) }
                        }
                    }
                }
            }
        }
    }

    fn render_solutions_section() -> Markup {
        let solutions = get_business_solutions();
        
        html! {
            div.section {
                h2 { "Answers" }
                @for solution in solutions {
                    div.answer-item {
                        div.arrow { "→" }
                        div.item-content {
                            p { (solution) }
                        }
                    }
                }
            }
        }
    }

    // Data structures and helper functions
    #[derive(Clone)]
    struct TimelineProject {
        years: &'static str,
        title: &'static str,
        description: &'static str,
    }

    fn get_timeline_projects() -> Vec<TimelineProject> {
        vec![
            TimelineProject {
                years: "2004 — 21 Years (Active Project)",
                title: "Advanced Reservation System",
                description: "Connected staff, guides, clients, guests, housekeeping together in one unified scheduling portal for an exclusive Pennsylvania Fishing club.",
            },
            TimelineProject {
                years: "2005 — 15 Years (Inactive Project)",
                title: "CRM & Marketing Software",
                description: "Constructed a database system to collect and distribute millions of leads to thousands of small business owners nationwide via a unique filtering engine and CRM.",
            },
            TimelineProject {
                years: "2009 — 16 Years (Active Project)",
                title: "Client Marketing Dashboard",
                description: "Built a custom marketing planning community and content delivery platform used by thousands of small businesses to access premium content, community, and the tools they need to grow their businesses.",
            },
            TimelineProject {
                years: "2013 — 12 Years (Active Project)",
                title: "Technical Investment in ACRM",
                description: "Engineered and implemented a set of foundational modules that became the basis for all AppCove software going forward.",
            },
            TimelineProject {
                years: "2016 — 9 Years (Active Project)",
                title: "Dashboard as a Service",
                description: "Designed a platform used by coaching organizations to serve their clients with content, training, community, and communications. Used by successful organizations in areas such as IT, Trades, Finance, and Law.",
            },
            TimelineProject {
                years: "2016 — 9 Years (Active Project)",
                title: "Tools for Financial Advisors",
                description: "Created custom software representing unique finance and investment products. Hundreds of financial advisors have used these to serve tens of thousands of clients.",
            },
        ]
    }

    fn get_business_problems() -> Vec<&'static str> {
        vec![
            "Business Data is contained in a growing number of SaaS providers that do not communicate well with each other.",
            "Premium Customer Experience can be difficult to provide when using a collection of off-the-shelf SaaS tools.",
            "Custom software can be slow to build, with uncertain outcomes, and may lack long term support.",
            "Artificial intelligence fails to maximize value if it does not have access to comprehensive, well-structured business data.",
        ]
    }

    fn get_business_solutions() -> Vec<&'static str> {
        vec![
            "AppCove has an intense focus on well-designed custom databases for each client we work with. Having all key data in one central database allows everything else to fall into place.",
            "AppCove provides a unified experience with correct security and access controls covering all stakeholders: owners / administrators / staff / customers / end users.",
            "AppCove's pursuit of technical excellence, coupled with a willingness to learn the details of your business results in efficient code, streamlined implementation, and a custom software product built for long-term support.",
            "AppCove's rigorous database design approach positions each client to maximize the value of artificial intelligence by providing AI systems with comprehensive, well-structured access to your business data.",
        ]
    }
}