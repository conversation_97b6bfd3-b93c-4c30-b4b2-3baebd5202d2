#[approck::http(GET /; AUTH None; return HTML;)]
pub mod page {
pub async fn request(doc: Document) -> Response {
    use maud::html;

    doc.set_title("AppCove");

    doc.add_body(html!(
        div.container.mt-4 {
            div.row {
                    div.col-12 {
                        h1 { "AppCove,Inc."}

                        p { "Welcome to AppCove." }

                        p { 
                            "We build custom software focused on providing 
                            span class customers
                            with a premium experience"
                        }
                        p.important-text {
                            "AppCove has a proven track record of building and supporting reliable web applications for over 20 years."
                        }
                        p {
                            "This is reflected in every aspect of our work — from our people and training to our software and contracts..."
                        }
                        div.pair-container {
                            div.row-group {
                                div.col-half { p { "Problem" } }
                                div.col-small { i class="fas fa-arrow-right" {} }
                                div.col-half { p { "Answer" } }
                            }
                        }
                        (
                            "Business Data is contained in a growing number of SaaS providers that do not communicate well with each other.",
                            "AppCove focuses on well-designed custom databases so all key data is in one place."
                        ),
                        (
                            "Premium Customer Experience is hard when using a mix of SaaS tools.",
                            "AppCove offers a unified experience with proper access control."
                        ),
                        (
                            "Custom software is often slow to build with uncertain outcomes.",
                            "AppCove delivers efficient code and long-term support through technical excellence."
                        ),
                        (
                            "AI can’t provide value without reliable structured data.",
                            "AppCove’s database design prepares clients for AI integration."
                        )

                        
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}
