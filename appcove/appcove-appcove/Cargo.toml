[package]
name = "appcove-appcove"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[package.metadata.acp]
app.port = 3016

extends = ["appcove-appcove-zersprint-2025-q1$ ./acp check
error: failed to load manifest for workspace member `/home/<USER>/code/acp7r/p-sprint-2025-q1/ace/agent/ace-agent`
referenced by workspace at `/home/<USER>/code/acp7r/p-sprint-2025-q1/Cargo.toml`

Caused by:
  failed to load manifest for dependency `approck`

Caused by:
  failed to load manifest for dependency `chasetls`

Caused by:
  failed to parse manifest at `/home/<USER>/code/acp7r/p-sprint-2025-q1/lib/chasetls/Cargo.toml`

Caused by:
  error inheriting `x509-parser` from workspace root manifest's `workspace.dependencies.x509-parser`

Caused by:
  `dependency.x509-parser` was not found in `workspace.dependencies`
sherrie@sherrie-ThinkBook:~/code/acp7r/p-sprint-2025-q1$ 
o", "appcove-appcove-public", "approck", "bux", "granite", "auth-fence", "auth-fence-provider"]

[dependencies]
appcove-appcove-zero = { path = "../appcove-appcove-zero" }
appcove-appcove-public = { path = "../appcove-appcove-public" }
approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
approck-redis = { workspace = true }
approck-postgres = { workspace = true }
auth-fence = { workspace = true }
auth-fence-provider = { workspace = true }


clap = { workspace = true, features = ["derive"] }
tokio = { workspace = true, features = ["full"] }
maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
toml = { workspace = true }
chrono = { workspace = true, features = ["serde"] }


