#[approck::http(GET /images/AppCove.png; AUTH None; return Bytes;)]
pub mod appcove_png {
    pub async fn request() -> Response {
        let image_bytes = include_bytes!("../../../appcove-web/src/images/AppCove_Profile.png");
        Response::Bytes(Bytes::new(image_bytes.as_slice()))
    }
}

#[approck::http(GET /images/appcove_logo_2.png; AUTH None; return Bytes;)]
pub mod appcove_logo_2_png {
    pub async fn request() -> Response {
        let image_bytes = include_bytes!("../../../appcove-web/src/images/appcove_logo_2.png");
        Response::Bytes(Bytes::new(image_bytes.as_slice()))
    }
}
