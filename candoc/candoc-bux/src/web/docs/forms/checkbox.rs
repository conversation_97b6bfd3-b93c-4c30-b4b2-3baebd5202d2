#[approck::http(GET /docs/forms/checkbox; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.add_body(html!(
            panel {
                content {
                    grid-12 {
                        cell-3 {
                            (super::super::super::render_docs_nav())
                        }
                        cell-6 {
                            h1 { "Checkbox input" }
                            p { "Checkboxes are for selecting one or several options in a list. Current support includes the following use cases:" }
                            ul {
                                li {
                                    code { "bux_checkbox" }
                                }
                                li {
                                    code { "bux_checkbox_with_help" }
                                }
                            }

                            p { b { "Example" } }
                            panel {
                                content style="padding-bottom: 0;" {
                                    (bux::input::checkbox::name_label_checked("checkbox_example", "Checkbox label", false))
                                }
                                footer {
                                    pre {
                                        code {
                                            "(bux::input::checkbox::bux_checkbox(\n"
                                            "    \"checkbox_example\",\n"
                                            "    \"Checkbox label\",\n"
                                            "    false,\n"
                                            "))"
                                        }
                                    }
                                }
                            }
                        }
                        cell-3 {
                            h3 { "On this page" }
                            hr;
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}
