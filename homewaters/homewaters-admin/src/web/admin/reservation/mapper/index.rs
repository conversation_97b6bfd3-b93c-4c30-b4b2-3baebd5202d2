#[approck::http(GET /admin/reservation/{admin_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.page_nav_enable_go_back("/admin/reservation/calendar");
        doc.set_title("Summary of Reservation for Robert Bruce");

        doc.add_body(html! {
            panel {
                content {
                    dl {
                        dt { "Email sent on 04/15/2024:" }
                        dd { a href="mailto:<EMAIL>" { "<EMAIL>" } }
                        dd { a href="mailto:<EMAIL>" { "<EMAIL>" } }
                        dt { "Email sent on 10/04/2024:" }
                        dd { a href="mailto:<EMAIL>" { "<EMAIL>" } }
                        dd { a href="mailto:<EMAIL>" { "<EMAIL>" } }
                        dt { "Reservation #:" }
                        dd { "19459" }
                        dt { "Booked:" }
                        dd { "Saturday, April 13, 2024" }
                        dt { "Arrival Date:" }
                        dd { "Monday, March 3, 2025" }
                        dt { "Time:" }
                        dd { "3:00 pm" }
                        dt { "Departure Date:" }
                        dd { "Wednesday, March 5, 2025" }
                        dt { "Time:" }
                        dd { "N/A" }
                        dt { "Remarks:" }
                        dd { "Using Member Access. Day #7 of 2024. Guide/water requested." }
                    }
                }
            }
            table-wrapper.data-list {
                table {
                    thead {
                        tr {
                            th colspan="2" {
                                b { "Monday, March 3, 2025" }
                            }
                            th colspan="5" {
                                "Special " a href="#" { "<Hosts>" } ":"
                                br;
                                "Default " a href="#" { "<Hosts>" } ":"
                            }
                            th colspan="3" { "Meals" }
                            th colspan="3" { "Services" }
                            th { "" }
                        }
                        tr {
                            th { "Name" }
                            th { "Lodge" }
                            th { "Guide" }
                            th { "Beat" }
                            th { "Misc" }
                            th { "Start Time" }
                            th { "End Time" }
                            th { "B" }
                            th { "L" }
                            th { "D" }
                            th { "Housekeeping" }
                            th { "Rods" }
                            th { "Waders" }
                            th { "Delete" }
                        }
                    }
                    tbody {
                        tr {
                            td { "Bruce & Associates PLC" br; "Robert Bruce" }
                            td { "Bark House - Jr. Suite" }
                            td { "" }
                            td { "- Arrival" }
                            td { "" }
                            td { "3:00 pm" }
                            td { "4:00 pm" }
                            td { "" }
                            td { "" }
                            td { "" }
                            td { "Trash Only" }
                            td { "" }
                            td { "" }
                            td { (bux::input::checkbox::name_label_checked("", "", false)) }
                        }
                        tr {}
                        tr {
                            td { "Laurie Bruce" }
                            td { "Bark House - Jr. Suite" }
                            td { "" }
                            td { "- Arrival" }
                            td { "" }
                            td { "3:00 pm" }
                            td { "4:00 pm" }
                            td { "" }
                            td { "" }
                            td { "" }
                            td { "Trash Only" }
                            td { "" }
                            td { "" }
                            td { (bux::input::checkbox::name_label_checked("", "", false)) }
                        }
                    }
                }
            }br;
            table-wrapper.data-list {
                table {
                    thead {
                        tr {
                            th colspan="2" {
                                b { "Tuesday, March 4, 2025" }
                            }
                            th colspan="5" {
                                "Special " a href="#" { "<Hosts>" } ":"
                                br;
                                "Default " a href="#" { "<Hosts>" } ":"
                            }
                            th colspan="3" { "Meals" }
                            th colspan="3" { "Services" }
                            th { "" }
                        }
                        tr {
                            th { "Name" }
                            th { "Lodge" }
                            th { "Guide" }
                            th { "Beat" }
                            th { "Misc" }
                            th { "Start Time" }
                            th { "End Time" }
                            th { "B" }
                            th { "L" }
                            th { "D" }
                            th { "Housekeeping" }
                            th { "Rods" }
                            th { "Waders" }
                            th { "Delete" }
                        }
                    }
                    tbody {
                        tr {
                            td { "Bruce & Associates PLC" br; "Robert Bruce" }
                            td { "Bark House - Jr. Suite" }
                            td { "- Andy Wagner" }
                            td { "Spruce - Six Springs" }
                            td { "" }
                            td { "10:30 am" }
                            td { "4:30 pm" }
                            td { "Y" }
                            td { "Y" }
                            td { "Y" }
                            td { "Trash Only" }
                            td { "" }
                            td { "" }
                            td { (bux::input::checkbox::name_label_checked("", "", false)) }
                        }
                        tr {
                            td { "Laurie Bruce" }
                            td { "Bark House - Jr. Suite" }
                            td { "- Andy Wagner" }
                            td { "Spruce - Six Springs" }
                            td { "" }
                            td { "10:30 am" }
                            td { "4:30 pm" }
                            td { "Y" }
                            td { "Y" }
                            td { "Y" }
                            td { "Trash Only" }
                            td { "" }
                            td { "" }
                            td { (bux::input::checkbox::name_label_checked("", "", false)) }
                        }
                    }
                }
            }br;
            table-wrapper.data-list {
                table {
                    thead {
                        tr {
                            th colspan="2" {
                                b { "Wednesday, March 5, 2025" }
                            }
                            th colspan="5" {
                                "Special " a href="#" { "<Hosts>" } ":"
                                br;
                                "Default " a href="#" { "<Hosts>" } ":"
                            }
                            th colspan="3" { "Meals" }
                            th colspan="3" { "Services" }
                            th { "" }
                        }
                        tr {
                            th { "Name" }
                            th { "Lodge" }
                            th { "Guide" }
                            th { "Beat" }
                            th { "Misc" }
                            th { "Start Time" }
                            th { "End Time" }
                            th { "B" }
                            th { "L" }
                            th { "D" }
                            th { "Housekeeping" }
                            th { "Rods" }
                            th { "Waders" }
                            th { "Delete" }
                        }
                    }
                    tbody {
                        tr {
                            td { "Bruce & Associates PLC" br; "Robert Bruce" }
                            td { "" }
                            td { "" }
                            td { "- Departure" }
                            td { "" }
                            td { "9:00 am" }
                            td { "10:00 am" }
                            td { "" }
                            td { "" }
                            td { "" }
                            td { "Full Service" }
                            td { "" }
                            td { "" }
                            td { (bux::input::checkbox::name_label_checked("", "", false)) }
                        }
                        tr {
                            td { "Laurie Bruce" }
                            td { "" }
                            td { "" }
                            td { "- Departure" }
                            td { "" }
                            td { "9:00 am" }
                            td { "10:00 am" }
                            td { "" }
                            td { "" }
                            td { "" }
                            td { "Full Service" }
                            td { "" }
                            td { "" }
                            td { (bux::input::checkbox::name_label_checked("", "", false)) }
                        }
                    }
                }
            }
        });
        Response::HTML(doc.into())
    }
}
