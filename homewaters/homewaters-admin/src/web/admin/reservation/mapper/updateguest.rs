#[approck::http(GET /admin/reservation/{admin_uuid:Uuid}/updateguest; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Update Reservation for <PERSON>");

        doc.add_body(html! {
            bux-action-panel {
                panel {
                    content {
                        grid-2 {
                            (bux::input::date::bux_input_date("arrival_date", "Arrival Date", None))
                            (bux::input::text::string::name_label_value("time", "Time", None))
                            (bux::input::date::bux_input_date("departure_date", "Departure Date", None))
                            (bux::input::text::string::name_label_value("time", "Time", None))
                        }
                        (bux::input::textarea::string::name_label_value("remarks", "Remarks", None))
                    }
                }hr;
                table-wrapper.data-list.mb-2 {
                    h4 { "Guest Details" }
                    p { "If you would like to add additional guests to this registration, please select them below" }
                    table {
                        thead {
                            tr {
                                th { "Name" }
                                th { "Relationship" }
                                th { "Add" }
                                th { "Replace With" }
                            }
                        }
                        tbody {
                            tr {
                                td { "Chad Bales" }
                                td { "Friend" }
                                td { (bux::input::checkbox::name_label_checked("", "", false)) }
                                td { (bux::input::select::nilla::nilla_select("", "", &[], None)) }
                            }
                            tr {
                                td { "Kirk Brady" }
                                td { "Extended Family" }
                                td { (bux::input::checkbox::name_label_checked("", "", false)) }
                                td { (bux::input::select::nilla::nilla_select("", "", &[], None)) }
                            }
                            tr {
                                td { "Laurie Bruce" }
                                td { "Immediate Family" }
                                td { (bux::input::checkbox::name_label_checked("", "", false)) }
                                td { (bux::input::select::nilla::nilla_select("", "", &[], None)) }
                            }
                            tr {
                                td { "Frank Lewis" }
                                td { "Friend" }
                                td { (bux::input::checkbox::name_label_checked("", "", false)) }
                                td { (bux::input::select::nilla::nilla_select("", "", &[], None)) }
                            }
                            tr {
                                td { "Pat Mason" }
                                td { "Friend" }
                                td { (bux::input::checkbox::name_label_checked("", "", false)) }
                                td { (bux::input::select::nilla::nilla_select("", "", &[], None)) }
                            }
                        }
                    }
                }
                grid-2 {
                    (bux::input::text::string::name_label_value("", "Add unknown guests to this reservation", None))
                }hr;
                (bux::button::link::label_icon_class("Update Reservation", "", "/admin/reservation/00000000-0000-0000-0000-000000000000/edit", "primary"))
                " "
                (bux::button::link::cancel("/admin/reservation/00000000-0000-0000-0000-000000000000/"))
            }
        });
        Response::HTML(doc.into())
    }
}
