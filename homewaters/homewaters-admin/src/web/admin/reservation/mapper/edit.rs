#[approck::http(GET /admin/reservation/{admin_uuid:Uuid}/edit?complete; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document, qs: QueryString) -> Response {
        let is_complete: bool = qs.complete;
        use maud::html;

        doc.page_nav_enable_go_back("/admin/reservation/calendar");
        doc.set_title("Reservation for Robert Bruce");

        doc.add_body(html! {
            panel {
                content {
                    dl {
                        dt { "Arrival Date:" }
                        dd { "Monday, March 3, 2025" }
                        dt { "Time:" }
                        dd { "3:00 pm" }
                        dt { "Departure Date:" }
                        dd { "Wednesday, March 5, 2025" }
                        dt { "Time:" }
                        dd { "N/A" }
                        dt { "Reservation Status:" }
                        dd { "DEFAULT" }
                        dt { "Remarks:" }
                        dd { "None" }
                    }
                }
            }
            p.mb-0 { i { "Note: " b { "Monday 3/3/25" } ", and '" b { "Finalize Reservation" } "' are clickable because they lead to different screens." } }
            h4 { "Select Date to Edit" }
            bux-hr-list {
                ul {
                    li { a href="/admin/reservation/00000000-0000-0000-0000-000000000000/edit" { "Monday" br; "3/3/25" } }
                    li { a href="#" { "Tuesday" br; "3/4/25" } }
                    li { a href="#" { "Wednesday" br; "3/5/25" } }
                    li { a href="/admin/reservation/00000000-0000-0000-0000-000000000000/edit?complete" { "Finalize" br; "Reservation" } }
                }
            }br;
            @if is_complete {
                complete-reservation {
                    (bux::input::select::nilla::nilla_select("reservation_type", "Reservation Type", &[], None))
                    (bux::button::link::label_icon_class("Confirm ", "", "#", "secondary mb-3"))
                    " "
                    (bux::button::link::label_icon_class("Leave Pending ", "", "#", "secondary mb-3"))
                    " "
                    (bux::button::link::label_icon_class("Cancel Reservation ", "", "#", "secondary mb-3"))
                    " "
                    (bux::button::link::label_icon_class("Void Reservation ", "", "#", "secondary mb-3"))
                    table-wrapper.data-list {
                        table {
                            thead {
                                tr {
                                    th colspan="7" { b { "Monday, March 3, 2025" } }
                                    th colspan="3" { "Meals" }
                                    th colspan="3" { "Services" }
                                }
                                tr {
                                    th { "Name" }
                                    th { "Lodge" }
                                    th { "Guide" }
                                    th { "Beat" }
                                    th { "Misc" }
                                    th { "Start Time" }
                                    th { "End Time" }
                                    th { "B" }
                                    th { "L" }
                                    th { "D" }
                                    th { "Housekeeping" }
                                    th { "Rods" }
                                    th { "Waders" }
                                }
                            }
                            tbody {
                                tr {
                                    td { "Robert Bruce" }
                                    td { "Bark House - Jr. Suite" }
                                    td { "" }
                                    td { "- Arrival" }
                                    td { "" }
                                    td { "3:00 pm" }
                                    td { "4:00 pm" }
                                    td { "" }
                                    td { "" }
                                    td { "" }
                                    td { "Trash Only" }
                                    td { "" }
                                    td { "" }
                                }
                                tr {
                                    td { "Laurie Bruce" }
                                    td { "Bark House - Jr. Suite" }
                                    td { "" }
                                    td { "- Arrival" }
                                    td { "" }
                                    td { "3:00 pm" }
                                    td { "4:00 pm" }
                                    td { "" }
                                    td { "" }
                                    td { "" }
                                    td { "Trash Only" }
                                    td { "" }
                                    td { "" }
                                }
                            }
                        }
                    }br;
                    table-wrapper.data-list {
                        table {
                            thead {
                                tr {
                                    th colspan="7" { b { "Tuesday, March 4, 2025" } }
                                    th colspan="3" { "Meals" }
                                    th colspan="3" { "Services" }
                                }
                                tr {
                                    th { "Name" }
                                    th { "Lodge" }
                                    th { "Guide" }
                                    th { "Beat" }
                                    th { "Misc" }
                                    th { "Start Time" }
                                    th { "End Time" }
                                    th { "B" }
                                    th { "L" }
                                    th { "D" }
                                    th { "Housekeeping" }
                                    th { "Rods" }
                                    th { "Waders" }
                                }
                            }
                            tbody {
                                tr {
                                    td { "Robert Bruce" }
                                    td { "Bark House - Jr. Suite" }
                                    td { "- Andy Wagner" }
                                    td { "Spruce - Six Springs" }
                                    td { "" }
                                    td { "10:30 am" }
                                    td { "4:30 pm" }
                                    td { "Y" }
                                    td { "Y" }
                                    td { "Y" }
                                    td { "Trash Only" }
                                    td { "" }
                                    td { "" }
                                }
                                tr {
                                    td { "Laurie Bruce" }
                                    td { "Bark House - Jr. Suite" }
                                    td { "- Andy Wagner" }
                                    td { "Spruce - Six Springs" }
                                    td { "" }
                                    td { "10:30 am" }
                                    td { "4:30 pm" }
                                    td { "Y" }
                                    td { "Y" }
                                    td { "Y" }
                                    td { "Trash Only" }
                                    td { "" }
                                    td { "" }
                                }
                            }
                        }
                    }br;
                    table-wrapper.data-list {
                        table {
                            thead {
                                tr {
                                    th colspan="7" { b { "Wednesday, March 5, 2025" } }
                                    th colspan="3" { "Meals" }
                                    th colspan="3" { "Services" }
                                }
                                tr {
                                    th { "Name" }
                                    th { "Lodge" }
                                    th { "Guide" }
                                    th { "Beat" }
                                    th { "Misc" }
                                    th { "Start Time" }
                                    th { "End Time" }
                                    th { "B" }
                                    th { "L" }
                                    th { "D" }
                                    th { "Housekeeping" }
                                    th { "Rods" }
                                    th { "Waders" }
                                }
                            }
                            tbody {
                                tr {
                                    td { "Robert Bruce" }
                                    td { "" }
                                    td { "" }
                                    td { "- Departure" }
                                    td { "" }
                                    td { "9:00 am" }
                                    td { "10:00 am" }
                                    td { "" }
                                    td { "" }
                                    td { "" }
                                    td { "Full Service" }
                                    td { "" }
                                    td { "" }
                                }
                                tr {
                                    td { "Laurie Bruce" }
                                    td { "" }
                                    td { "" }
                                    td { "- Departure" }
                                    td { "" }
                                    td { "9:00 am" }
                                    td { "10:00 am" }
                                    td { "" }
                                    td { "" }
                                    td { "" }
                                    td { "Full Service" }
                                    td { "" }
                                    td { "" }
                                }
                            }
                        }
                    }
                }
            }
            @else {
                btn-wrapper {
                    (bux::button::link::label_icon_class("Next ", "", "#", "secondary mb-3"))
                    " "
                    (bux::button::link::label_icon_class("Cancel Reservation ", "", "#", "secondary mb-3"))
                    " "
                    (bux::button::link::label_icon_class("Void Reservation ", "", "#", "secondary mb-3"))
                }br;
                bux-action-panel {
                    h4 { "Lodging Arrangements" }
                    table-wrapper.data-list {
                        table {
                            thead {
                                tr {
                                    th { "Member/Guest" }
                                    th { "Lodge" }
                                    th { "Housekeeping" }
                                }
                            }
                            tbody {
                                tr {
                                    td { "Robert Bruce" }
                                    td { (bux::input::select::nilla::nilla_select("", "", &[], None)) }
                                    td { (bux::input::select::nilla::nilla_select("", "", &[], None)) }
                                }
                                tr {
                                    td { "Laurie Bruce" }
                                    td { (bux::input::select::nilla::nilla_select("", "", &[], None)) }
                                    td { (bux::input::select::nilla::nilla_select("", "", &[], None)) }
                                }
                            }
                        }
                    }
                }br;
                h4 { "Activities & Services" }
                table-wrapper.data-list {
                    table {
                        thead {
                            tr {
                                th { "Member/Guest Guide" }
                                th { "Fishing Beat" }
                                th { "Other Activities" }
                                th { "Start Hour" }
                                th { "# of Hours" }
                                th { "Rod Needed" }
                                th { "Waders Needed" }
                                th { "" }
                            }
                        }
                        tbody {
                            tr {
                                td { "Robert Bruce" }
                                td { (bux::input::select::nilla::nilla_select("", "", &[], None)) }
                                td { (bux::input::select::nilla::nilla_select("", "", &[], None)) }
                                td { (bux::input::select::nilla::nilla_select("", "", &[], None)) }
                                td { (bux::input::select::nilla::nilla_select("", "", &[], None)) }
                                td { (bux::input::checkbox::name_label_checked("", "", false)) }
                                td { (bux::input::select::nilla::nilla_select("", "", &[], None)) }
                                td { (bux::button::link::label_icon_class("Add ", "fas fa-plus", "#", "primary")) }
                            }
                            tr {
                                td { "Laurie Bruce" }
                                td { (bux::input::select::nilla::nilla_select("", "", &[], None)) }
                                td { (bux::input::select::nilla::nilla_select("", "", &[], None)) }
                                td { (bux::input::select::nilla::nilla_select("", "", &[], None)) }
                                td { (bux::input::select::nilla::nilla_select("", "", &[], None)) }
                                td { (bux::input::checkbox::name_label_checked("", "", false)) }
                                td { (bux::input::select::nilla::nilla_select("", "", &[], None)) }
                                td { (bux::button::link::label_icon_class("Add ", "fas fa-plus", "#", "primary")) }
                            }
                        }
                    }
                }br;
                bux-action-panel {
                    h4 { "Meals" }
                    table-wrapper.data-list {
                        table {
                            thead {
                                tr {
                                    th { "Member/Guest" }
                                    th { "Breakfast" }
                                    th { "Lunch" }
                                    th { "Dinner" }
                                }
                            }
                            tbody {
                                tr {
                                    td { "Robert Bruce" }
                                    td { (bux::input::checkbox::name_label_checked("", "", false)) }
                                    td { (bux::input::checkbox::name_label_checked("", "", false)) }
                                    td { (bux::input::checkbox::name_label_checked("", "", false)) }
                                }
                                tr {
                                    td { "Laurie Bruce" }
                                    td { (bux::input::checkbox::name_label_checked("", "", false)) }
                                    td { (bux::input::checkbox::name_label_checked("", "", false)) }
                                    td { (bux::input::checkbox::name_label_checked("", "", false)) }
                                }
                            }
                        }
                    }
                }
            }
        });
        Response::HTML(doc.into())
    }
}
