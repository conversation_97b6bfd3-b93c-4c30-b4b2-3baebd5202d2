#[approck::http(GET /admin/reservation/{admin_uuid:Uuid}/email; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Send confirmation email for reservation - <PERSON>");

        doc.add_body(html! {
            bux-action-panel {
                panel {
                    content {
                        (bux::input::text::string::name_label_value("members_email", "Member's Email", None))
                        (bux::input::text::string::name_label_value("members_name", "Secondary Recipient", None))
                        (bux::input::textarea::string::name_label_value("remarks", "Remarks", None))
                        (bux::input::checkbox::name_label_checked("admin_copy", "Send Admin Copy", false))
                    }
                    footer {
                        (bux::button::submit::save("Send"))
                        (bux::button::link::cancel("/admin/reservation/00000000-0000-0000-0000-000000000000/"))
                    }
                }
            }
        });
        Response::HTML(doc.into())
    }
}
