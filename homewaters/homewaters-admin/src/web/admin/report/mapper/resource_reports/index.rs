#[approck::http(GET /admin/report/{admin_uuid:Uuid}/resource-reports/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.page_nav_enable_go_back("/admin/report/");
        doc.set_title("");

        doc.add_body(html! {
            bux-border-panel {
                h5 { "Resource Reports" }
                p { "Select the resource types you would like to include in the report." }
                (bux::input::checkbox::name_label_checked("check", " I would like to print the resource details", false))
                hr;
                (bux::input::checkbox::name_label_checked("fishing_guide", " Fishing Guide", false))
                (bux::input::checkbox::name_label_checked("lodging", " Lodging", false))
                (bux::input::checkbox::name_label_checked("other_resources", " Other Resources", false))
                (bux::input::checkbox::name_label_checked("water_beat", " Water Beat", false))
            }
        });
        Response::HTML(doc.into())
    }
}
