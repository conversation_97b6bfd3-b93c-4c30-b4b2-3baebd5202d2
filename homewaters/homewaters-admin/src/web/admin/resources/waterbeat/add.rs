#[approck::http(GET /admin/resources/waterbeat/add; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Add a Water Beat Source");

        let mut panel = bux::component::save_cancel_form_panel("", "/admin/resources/waterbeat/");

        #[rustfmt::skip]
        panel.add_body(maud::html!(
            grid-2 {
                (bux::input::text::string::name_label_value("header_desciption", "Header Description", None))
                (bux::input::text::string::name_label_value("desciption", "Description", None))
                (bux::input::text::string::name_label_value("watershed", "Watershed", None))
                (bux::input::select::nilla::nilla_select("map_url", "Map URL", &[], None))
            }
            grid-2 {
                (bux::input::date::bux_input_date("date", "Active Start Date", None))
                (bux::input::date::bux_input_date("date", "Active End Date", None))
                (bux::input::select::state::state_input("status", "Status", None))
                (bux::input::text::string::name_label_value("availability", "Availability", None))
            }
                h5 { "Choose the unavailable days for schedule defaults:" }
                (bux::input::checkbox::name_label_checked("monday", "Monday", false))
                (bux::input::checkbox::name_label_checked("tuesday", "Tuesday", false))
                (bux::input::checkbox::name_label_checked("wednesday", "Wednesday", false))
                (bux::input::checkbox::name_label_checked("thursday", "Thursday", false))
                (bux::input::checkbox::name_label_checked("friday", "Friday", false))
                (bux::input::checkbox::name_label_checked("saturday", "Saturday", false))
                (bux::input::checkbox::name_label_checked("sunday", "Sunday", false))

                h5 { "Please choose rest criteria for this resource:" }
                (bux::input::checkbox::name_label_checked("rest_one_day_per_week", "Rest 1 day per week", false))
                (bux::input::checkbox::name_label_checked("rods_and_reels", "Rest 2 day per week", false))
                (bux::input::checkbox::name_label_checked("rods_and_reels", "Rest 3 day per week", false))
                (bux::input::checkbox::name_label_checked("waders", "Rest every other day", false))
                (bux::input::checkbox::name_label_checked("rods_and_reels", "Fish no more than 2 consecutive days", false))

                h5 { "This resource is associated with the following services:" }
                (bux::input::checkbox::name_label_checked("rods_and_reels", "Rods and Reels", false))
                (bux::input::checkbox::name_label_checked("waders", "Waders", false))

                (bux::input::text::string::name_label_value("remarks", "Remarks", None))
        ));
        doc.add_body(html! {
            section {
                (panel)
            }
        });
        Response::HTML(doc.into())
    }
}
