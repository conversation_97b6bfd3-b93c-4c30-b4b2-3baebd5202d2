#[approck::http(GET /admin/resources/otherresources/{admin_uuid:Uuid}/edit; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Edit Resource: Other Resources");

        let mut panel = bux::component::save_cancel_form_panel(
            "",
            "/admin/resources/otherresources/00000000-0000-0000-0000-000000000000/",
        );
        #[rustfmt::skip]
        panel.add_body(maud::html!(
            grid-2 {
                cell-6 {
                    grid-2 {
                        (bux::input::text::string::name_label_value("header_desciption", "Header Description", None))
                        (bux::input::text::string::name_label_value("description", "Description", None))
                        (bux::input::text::string::name_label_value("url", "URL", None))
                        (bux::input::select::state::state_input("status", "Status", None))
                    }
                    grid-2 {
                        (bux::input::date::bux_input_date("date", "Active Start Date", None))
                        (bux::input::date::bux_input_date("date", "Active End Date", None))
                    }
                        h5 { "Choose the unavailable days for schedule defaults:" }
                        (bux::input::checkbox::name_label_checked("monday", "Monday", false))
                        (bux::input::checkbox::name_label_checked("tuesday", "Tuesday", false))
                        (bux::input::checkbox::name_label_checked("wednesday", "Wednesday", false))
                        (bux::input::checkbox::name_label_checked("thursday", "Thursday", false))
                        (bux::input::checkbox::name_label_checked("friday", "Friday", false))
                        (bux::input::checkbox::name_label_checked("saturday", "Saturday", false))
                        (bux::input::checkbox::name_label_checked("sunday", "Sunday", false))
                    (bux::input::text::string::name_label_value("remarks", "Remarks", None))
                }
            }
        ));
        doc.add_body(html! {
            section {
                (panel)
            }
        });
        Response::HTML(doc.into())
    }
}
