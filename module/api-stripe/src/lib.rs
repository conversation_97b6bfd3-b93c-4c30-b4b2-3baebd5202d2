pub mod api;
mod stripe;
pub mod web;

pub trait App: approck::App + approck_postgres::App {
    fn stripe(&self) -> &ModuleStruct;
}

pub trait Identity: approck::Identity {}

pub trait Document: bux::document::Base {}

#[derive(serde::Deserialize)]
pub struct ModuleConfig {
    pub secret_key: String,
    pub public_key: String,
}
pub struct ModuleStruct {
    secret_key: String,
    #[allow(dead_code)]
    public_key: String, //don't use it now
}

impl approck::Module for ModuleStruct {
    type Config = ModuleConfig;
    fn new(config: Self::Config) -> granite::Result<Self> {
        Ok(Self {
            secret_key: config.secret_key,
            public_key: config.public_key,
        })
    }
    async fn init(&self) -> granite::Result<()> {
        Ok(())
    }
}
impl ModuleStruct {
    pub async fn create_customer(
        &self,
        name: &str,
        email: &str,
    ) -> granite::Result<stripe::StripeCustomerResponse> {
        let api_key = &self.secret_key;
        stripe::create_customer(api_key, name, email).await
    }

    pub async fn create_billing_portal_link(
        &self,
        customer_id: &str,
        return_url: &str,
        configuration: Option<&str>,
    ) -> granite::Result<stripe::StripeBillingPortalResponse> {
        let api_key = &self.secret_key;
        stripe::create_billing_portal_link(api_key, customer_id, return_url, configuration).await
    }

    pub async fn checkout_session_create(
        &self,
        customer_id: &str,
        success_url: &str,
        cancel_url: &str,
    ) -> granite::Result<stripe::StripeCheckoutSessionResponse> {
        let api_key = &self.secret_key;
        stripe::checkout_session_create(api_key, customer_id, success_url, cancel_url).await
    }
    pub async fn checkout_session_get(
        &self,
        session_id: &str,
    ) -> granite::Result<stripe::StripeCheckoutSessionResponse> {
        let api_key = &self.secret_key;
        stripe::checkout_session_get(api_key, session_id).await
    }
    pub async fn product_get(
        &self,
        product_id: &str,
    ) -> granite::Result<stripe::StripeProductResponse> {
        let api_key = &self.secret_key;
        stripe::product_get(api_key, product_id).await
    }
    pub async fn price_get(&self, price_id: &str) -> granite::Result<stripe::StripePriceResponse> {
        let api_key = &self.secret_key;
        stripe::price_get(api_key, price_id).await
    }
}
