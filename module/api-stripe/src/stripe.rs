use granite::{Error, ErrorType};
use reqwest::header::{AUTHORIZATION, CONTENT_TYPE, HeaderMap, HeaderValue};
use serde::Deserialize;
use serde_json::Value;
use std::collections::HashMap;

#[derive(Deserialize, Debug)]
pub struct StripeCustomerResponse {
    pub id: String,
    pub object: String,
    pub email: String,
    pub name: String,
}

#[derive(Deserialize, Debug)]
pub struct StripeBillingPortalResponse {
    pub id: String,
    pub object: String,
    pub url: String,
    #[serde(default)]
    pub created: i64,
    #[serde(default)]
    pub customer: String,
    #[serde(default)]
    pub livemode: bool,
    #[serde(default)]
    pub return_url: Option<String>,
    #[serde(flatten)]
    pub additional_properties: HashMap<String, Value>,
}

pub async fn create_customer(
    api_key: &str,
    name: &str,
    email: &str,
) -> granite::Result<StripeCustomerResponse> {
    let url = "https://api.stripe.com/v1/customers";

    // Create auth header with API key
    let mut headers = HeaderMap::new();
    let auth_value = format!("Bearer {}", api_key);
    headers.insert(
        AUTHORIZATION,
        HeaderValue::from_str(&auth_value)
            .map_err(|e| Error::new(ErrorType::Unexpected).add_context(e.to_string()))?,
    );
    headers.insert(
        CONTENT_TYPE,
        HeaderValue::from_static("application/x-www-form-urlencoded"),
    );

    // Create form parameters
    let mut params = HashMap::new();
    params.insert("name", name);
    params.insert("email", email);

    // Send request to Stripe API
    let client = reqwest::Client::new();
    let response = client
        .post(url)
        .headers(headers)
        .form(&params)
        .send()
        .await
        .map_err(|e| {
            Error::new(ErrorType::Unexpected).add_context(format!("Failed to send request: {}", e))
        })?;

    // Parse response
    let customer: StripeCustomerResponse = response.json().await.map_err(|e| {
        Error::new(ErrorType::Unexpected).add_context(format!("Failed to parse response: {}", e))
    })?;

    Ok(customer)
}

pub async fn create_billing_portal_link(
    api_key: &str,
    customer_id: &str,
    return_url: &str,
    configuration_id: Option<&str>,
) -> granite::Result<StripeBillingPortalResponse> {
    /*
      https://docs.stripe.com/customer-management/integrate-customer-portal?shell=true&api=true&resource=billing_portal%20configurations&action=create

      API doc:
      https://docs.stripe.com/api/customer_portal/sessions/object

      curl https://api.stripe.com/v1/billing_portal/sessions \
    -u "sk_test_lq9XrBPArA2OZHwVSdl75keb:" \
    -d customer={{CUSTOMER_ID}} \
    --data-urlencode return_url="https://example.com/account"
      */
    let url = "https://api.stripe.com/v1/billing_portal/sessions";

    // Create auth header with API key
    let mut headers = HeaderMap::new();
    let auth_value = format!("Bearer {}", api_key);
    headers.insert(
        AUTHORIZATION,
        HeaderValue::from_str(&auth_value)
            .map_err(|e| Error::new(ErrorType::Unexpected).add_context(e.to_string()))?,
    );
    headers.insert(
        CONTENT_TYPE,
        HeaderValue::from_static("application/x-www-form-urlencoded"),
    );

    // Create form parameters
    let mut params = HashMap::new();
    params.insert("customer", customer_id);
    params.insert("return_url", return_url);
    /*"You passed an empty string for 'configuration'. We assume empty values are an attempt to unset a parameter; however 'configuration' cannot be unset. You should remove 'configuration' from your request or supply a non-empty value.
     */
    if let Some(configuration_id) = configuration_id {
        params.insert("configuration", configuration_id);
    }

    // Send request to Stripe API
    let client = reqwest::Client::new();
    let response = client
        .post(url)
        .headers(headers)
        .form(&params)
        .send()
        .await
        .map_err(|e| {
            Error::new(ErrorType::Unexpected).add_context(format!("Failed to send request: {}", e))
        })?;

    // Check if the response is successful
    if !response.status().is_success() {
        let status = response.status();
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unable to read error response".to_string());
        return Err(Error::new(ErrorType::Unexpected).add_context(format!(
            "Stripe API returned error status: {} with message: {}",
            status, error_text
        )));
    }

    // Get the response text for debugging
    let body_text = response.text().await?;
    println!("Raw Stripe billing portal response: {}", body_text);

    // Parse response
    let portal_session: StripeBillingPortalResponse =
        serde_json::from_str(&body_text).map_err(|e| {
            Error::new(ErrorType::Unexpected).add_context(format!(
                "Failed to parse response: {}. Response body: {}",
                e, body_text
            ))
        })?;

    Ok(portal_session)
}

//https://docs.stripe.com/api/checkout/sessions/create
pub async fn checkout_session_create(
    api_key: &str,
    customer_id: &str,
    success_url: &str,
    cancel_url: &str,
) -> granite::Result<StripeCheckoutSessionResponse> {
    println!(
        "STRIPE: checkout_session_create for customer: {}",
        customer_id
    );
    let url = "https://api.stripe.com/v1/checkout/sessions";

    // Create auth header with API key
    let mut headers = HeaderMap::new();
    let auth_value = format!("Bearer {}", api_key);
    headers.insert(
        AUTHORIZATION,
        HeaderValue::from_str(&auth_value)
            .map_err(|e| Error::new(ErrorType::Unexpected).add_context(e.to_string()))?,
    );
    headers.insert(
        CONTENT_TYPE,
        HeaderValue::from_static("application/x-www-form-urlencoded"),
    );

    // Create form parameters
    let mut params = HashMap::new();
    params.insert("customer", customer_id);
    params.insert("success_url", success_url);
    params.insert("cancel_url", cancel_url);
    params.insert("mode", "subscription");
    //params.insert("mode", "setup");
    //currency
    //params.insert("currency", "usd");

    //subscription price item
    params.insert("line_items[0][price]", "price_1RYtYHH1mpXpEHoK4xOl7H6F"); //TODO move it to LOCAL.toml
    params.insert("line_items[0][quantity]", "1");

    // Metered price item
    params.insert("line_items[1][price]", "price_1RYts6H1mpXpEHoKJ8Np2Jxh"); //TODO move it to LOCAL.toml
    // quantity should not be specified where usage_type is `metered

    // Send request to Stripe API
    let client = reqwest::Client::new();
    let response = client
        .post(url)
        .headers(headers)
        .form(&params)
        .send()
        .await
        .map_err(|e| {
            Error::new(ErrorType::Unexpected).add_context(format!("Failed to send request: {}", e))
        })?;

    // Check if the response is successful
    if !response.status().is_success() {
        let status = response.status();
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unable to read error response".to_string());
        return Err(Error::new(ErrorType::Unexpected).add_context(format!(
            "Stripe API returned error status: {} with message: {}",
            status, error_text
        )));
    }

    // Get the response text for debugging
    let body_text = response.text().await?;
    println!("Raw Stripe checkout session response: {}", body_text);

    // Parse response
    let checkout_session: StripeCheckoutSessionResponse = serde_json::from_str(&body_text)
        .map_err(|e| {
            Error::new(ErrorType::Unexpected).add_context(format!(
                "Failed to parse response: {}. Response body: {}",
                e, body_text
            ))
        })?;

    Ok(checkout_session)
}

#[derive(Deserialize, Debug)]
pub struct StripeCheckoutSessionResponse {
    pub id: String,
    pub object: String,
    pub url: Option<String>,
    #[serde(default)]
    pub created: i64,
    #[serde(default)]
    pub livemode: bool,
    pub payment_status: String,
    pub subscription: Option<String>, //Note, url is only returned if payment_status is not "paid"
}

pub async fn checkout_session_get(
    api_key: &str,
    session_id: &str,
) -> granite::Result<StripeCheckoutSessionResponse> {
    println!("STRIPE: checkout_session_get for session: {}", session_id);

    let url = format!("https://api.stripe.com/v1/checkout/sessions/{}", session_id);

    // Create auth header with API key
    let mut headers = HeaderMap::new();
    let auth_value = format!("Bearer {}", api_key);
    headers.insert(
        AUTHORIZATION,
        HeaderValue::from_str(&auth_value)
            .map_err(|e| Error::new(ErrorType::Unexpected).add_context(e.to_string()))?,
    );
    headers.insert(
        CONTENT_TYPE,
        HeaderValue::from_static("application/x-www-form-urlencoded"),
    );

    // Send request to Stripe API
    let client = reqwest::Client::new();
    let response = client.get(url).headers(headers).send().await.map_err(|e| {
        Error::new(ErrorType::Unexpected).add_context(format!("Failed to send request: {}", e))
    })?;

    // Check if the response is successful
    if !response.status().is_success() {
        let status = response.status();
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unable to read error response".to_string());
        return Err(Error::new(ErrorType::Unexpected).add_context(format!(
            "Stripe API returned error status: {} with message: {}",
            status, error_text
        )));
    }

    // Get the response text for debugging
    let body_text = response.text().await?;
    println!("Raw Stripe checkout session response: {}", body_text);

    // Parse response
    let checkout_session: StripeCheckoutSessionResponse = serde_json::from_str(&body_text)
        .map_err(|e| {
            Error::new(ErrorType::Unexpected).add_context(format!(
                "Failed to parse response: {}. Response body: {}",
                e, body_text
            ))
        })?;

    Ok(checkout_session)
}
/*Retrieve a product
Retrieves the details of an existing product. Supply the unique product ID from either a product creation request or the product list, and Stripe will return the corresponding product information.
curl https://api.stripe.com/v1/products/prod_NWjs8kKbJWmuuc \
  -u "sk_test_51RWjsBH1mpXpEHoKnKIVNI3SCUUr1OebPVn019jRoyedSkj39lp2pzYrDwxkTplvS4aHU8nsjzHRQ0nb8GjlgxQ600OIHvC6VU:"
*/
pub async fn product_get(
    api_key: &str,
    product_id: &str,
) -> granite::Result<StripeProductResponse> {
    println!("STRIPE: product_get for product: {}", product_id);

    let url = format!("https://api.stripe.com/v1/products/{}", product_id);

    // Create auth header with API key
    let mut headers = HeaderMap::new();
    let auth_value = format!("Bearer {}", api_key);
    headers.insert(
        AUTHORIZATION,
        HeaderValue::from_str(&auth_value)
            .map_err(|e| Error::new(ErrorType::Unexpected).add_context(e.to_string()))?,
    );
    headers.insert(
        CONTENT_TYPE,
        HeaderValue::from_static("application/x-www-form-urlencoded"),
    );

    // Send request to Stripe API
    let client = reqwest::Client::new();
    let response = client.get(url).headers(headers).send().await.map_err(|e| {
        Error::new(ErrorType::Unexpected).add_context(format!("Failed to send request: {}", e))
    })?;

    // Check if the response is successful
    if !response.status().is_success() {
        let status = response.status();
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unable to read error response".to_string());
        return Err(Error::new(ErrorType::Unexpected).add_context(format!(
            "Stripe API returned error status: {} with message: {}",
            status, error_text
        )));
    }

    // Get the response text for debugging
    let body_text = response.text().await?;
    println!("Raw Stripe product response: {}", body_text);

    // Parse response
    let product: StripeProductResponse = serde_json::from_str(&body_text).map_err(|e| {
        Error::new(ErrorType::Unexpected).add_context(format!(
            "Failed to parse response: {}. Response body: {}",
            e, body_text
        ))
    })?;

    Ok(product)
}

#[derive(Deserialize, Debug)]
pub struct StripeProductResponse {
    pub id: String,
    pub object: String,
    pub active: bool,
    pub created: i64,
    pub livemode: bool,
    pub name: String,
    pub default_price: Option<String>,
}

/* Retrieve a price.
Retrieves the price with the given ID.
url https://api.stripe.com/v1/prices/price_1MoBy5LkdIwHu7ixZhnattbh \
  -u "sk_test_51RWjsBH1mpXpEHoKnKIVNI3SCUUr1OebPVn019jRoyedSkj39lp2pzYrDwxkTplvS4aHU8nsjzHRQ0nb8GjlgxQ600OIHvC6VU:"
  */
pub async fn price_get(api_key: &str, price_id: &str) -> granite::Result<StripePriceResponse> {
    println!("STRIPE: price_get for price: {}", price_id);

    let url = format!("https://api.stripe.com/v1/prices/{}", price_id);

    // Create auth header with API key
    let mut headers = HeaderMap::new();
    let auth_value = format!("Bearer {}", api_key);
    headers.insert(
        AUTHORIZATION,
        HeaderValue::from_str(&auth_value)
            .map_err(|e| Error::new(ErrorType::Unexpected).add_context(e.to_string()))?,
    );
    headers.insert(
        CONTENT_TYPE,
        HeaderValue::from_static("application/x-www-form-urlencoded"),
    );

    // Send request to Stripe API
    let client = reqwest::Client::new();
    let response = client.get(url).headers(headers).send().await.map_err(|e| {
        Error::new(ErrorType::Unexpected).add_context(format!("Failed to send request: {}", e))
    })?;

    // Check if the response is successful
    if !response.status().is_success() {
        let status = response.status();
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unable to read error response".to_string());
        return Err(Error::new(ErrorType::Unexpected).add_context(format!(
            "Stripe API returned error status: {} with message: {}",
            status, error_text
        )));
    }

    // Get the response text for debugging
    let body_text = response.text().await?;
    println!("Raw Stripe price response: {}", body_text);

    // Parse response
    let price: StripePriceResponse = serde_json::from_str(&body_text).map_err(|e| {
        Error::new(ErrorType::Unexpected).add_context(format!(
            "Failed to parse response: {}. Response body: {}",
            e, body_text
        ))
    })?;

    Ok(price)
}

#[derive(Deserialize, Debug)]
pub struct StripePriceResponse {
    pub id: String,
    pub object: String,
    pub active: bool,
    pub created: i64,
    pub livemode: bool,
    pub currency: String,
    pub unit_amount: Option<i64>,
    pub unit_amount_decimal: Option<String>,
    pub product: String,
}
