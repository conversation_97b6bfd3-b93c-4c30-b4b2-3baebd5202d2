#[approck::http(GET /admin/auth/role/{role_uuid:Uuid}/permissions; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        doc.add_css("/admin/auth/role/mapper/permissions.css");
        doc.add_js("/admin/auth/role/mapper/permissions.js");

        let role_uuid = path.role_uuid;

        // Get role details using the API
        let role = crate::api::admin::role::detail::detail::call(
            app,
            identity,
            crate::api::admin::role::detail::detail::Input { role_uuid },
        )
        .await?;

        // Get all permissions
        let all_permissions = crate::api::admin::permission::list::list::call(
            app,
            crate::api::admin::permission::list::list::Input { keyword: None },
        )
        .await?;

        // Get permissions associated with this role
        let role_permissions = crate::api::admin::role::permission::permission::call(
            app,
            identity,
            crate::api::admin::role::permission::permission::Input { role_uuid },
        )
        .await?;

        // Create a set of permission UUIDs that are already assigned to this role
        let assigned_permissions: std::collections::HashSet<_> = role_permissions
            .permissions
            .iter()
            .map(|p| p.permission_uuid)
            .collect();

        doc.set_title(&format!("Edit Permissions for Role: {}", role.name));
        doc.page_nav_enable_go_back(&format!("/admin/auth/role/{}/", role_uuid));

        // Create a form for editing permissions
        let mut form_panel = bux::component::save_cancel_form_panel(
            &format!("Edit Permissions for Role: {}", role.name),
            &format!("/admin/auth/role/{}/", role_uuid),
        );

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            input type="hidden" name="role_uuid" value=(role_uuid)
            div.permissions-list {
                h4 { "Select Permissions" }
                @for permission in &all_permissions.permissions {
                    div.permission-item {
                        (bux::input::checkbox::name_label_value_checked(
                            &format!("permissions[{}]", permission.permission_uuid),
                            &permission.name,
                            &permission.permission_uuid,
                            assigned_permissions.contains(&permission.permission_uuid)
                        ))
                    }
                }
            }
        ));

        doc.add_body(maud::html! {
            section {
                (form_panel)
            }
        });

        Ok(Response::HTML(doc.into()))
    }
}
