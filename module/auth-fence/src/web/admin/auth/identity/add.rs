#[approck::http(GET /admin/auth/identity/add; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Result<Response> {
        doc.add_css("/admin/auth/identity/add.css");
        doc.add_js("/admin/auth/identity/add.js");

        let type_options = crate::api::admin::identity::IdentityType::as_vec();
        let mut type_select_options = Vec::<(&str, &str)>::with_capacity(type_options.len());
        for opt in type_options.iter() {
            type_select_options.push((opt.as_str(), opt.as_str()));
        }

        let mut panel =
            bux::component::add_cancel_form_panel("Create Identity", "/admin/auth/identity/");
        #[rustfmt::skip]
        panel.add_body(maud::html!(
            (bux::input::select::nilla::nilla_select("identity_type", "Type", &type_select_options, None))
            (bux::input::text::string::name_label_value("name", "Name", None))
            (bux::input::text::string::name_label_value("email", "Email", None))
            (bux::input::textarea::string::name_label_value("note", "Note", None))
            (bux::input::text::string::name_label_value("avatar_uri", "Avatar URI", None))
            (bux::input::checkbox::name_label_checked("active", "Active", false))
        ));
        doc.add_body(maud::html! {
            identity-add-panel style="width: 50%; margin: 0 auto;" {
                section {
                    (panel)
                }
            }
        });

        Ok(Response::HTML(doc.into()))
    }
}
