admin-identity-detail {

    contact-info {
        text-align: center;
        display: block;

        h1 {
            font-size: 18pt;
        }

        *:last-child {
            margin-bottom: 0;
        }
    }

    dl {
        margin-bottom: 0;

        dt {
            font-weight: normal;
        }

        dd {

            &:last-child {
                margin-bottom: 0;
                font-size: 12pt;
            }

            label-tag {
                font-size: 9pt;
            }
        }
    }


    panel {

        header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 1rem;
            background-color: #fff !important;
            border-bottom: none !important;
            padding-top: 1rem !important;

            h5 {
                font-size: 1.25rem;
                font-weight: 600;
                margin-bottom: 0.25rem;
            }

            p {
                margin-bottom: 0;
            }
        }

        content {

            ul.data-list {
                margin-bottom: 0;
                padding-left: 0;
                list-style-type: none;

                li {
                    border-bottom: 1px solid #ddd;
                    padding: .75rem 0;

                    hbox {
                        align-items: flex-start;
                        justify-content: flex-start;
                        line-height: 1.25;

                        i {
                            width: 2rem;
                            text-align: center;
                            color: #777;
                            line-height: 1.25;
                        }

                        dl {
                            display: flex;
                            justify-content: flex-start;
                            margin-right: auto;

                            dt {
                                font-size: 12pt;

                                &:first-child {
                                    width: 20rem;
                                    flex-shrink: 0;
                                }
                            }
                        }
                    }

                    &:first-child {
                        border-top: 1px solid #ddd;
                    }
                }
            }
        }

        &.gray-tile {
            background-color: #f5f5f5;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            margin-bottom: 0;

            content {

                hbox {
                    align-items: center;
                    justify-content: flex-start;

                    i {
                        width: 3rem;
                        height: 3rem;
                        flex-shrink: 0;
                        background-color: white;
                        border-radius: .375rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #777;
                    }
                }
            }
        }

        /* Advisor Info Panel */
        &.identity-info {

            panel {

                &.gray-tile {
                    margin-bottom: 1rem;
                }
            }

            content {

                .edit-btn {
                    padding: 0;

                    i {
                        background-color: transparent;
                    }
                }
            }
        }
    }
}