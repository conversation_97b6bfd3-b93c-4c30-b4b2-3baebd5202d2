#[approck::http(GET /admin/auth/identity/{identity_uuid:Uuid}/edit; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        doc.add_js("/admin/auth/identity/mapper/edit.js");
        doc.add_css("/admin/auth/identity/mapper/edit.css");
        //doc.add_css("/admin/auth/mod.css");

        let identity_uuid = path.identity_uuid;
        let identity = crate::api::admin::identity::detail::detail::call(
            app,
            identity,
            crate::api::admin::identity::detail::detail::Input { identity_uuid },
        )
        .await?;

        let type_options = crate::api::admin::identity::IdentityType::as_vec();
        let mut type_select_options = Vec::<(&str, &str)>::with_capacity(type_options.len());
        for opt in type_options.iter() {
            type_select_options.push((opt.as_str(), opt.as_str()));
        }

        let mut panel = bux::component::save_cancel_form_panel(
            &format!("Edit Identity: {}", identity.name),
            &identity.ml_admin(""),
        );
        #[rustfmt::skip]
        panel.add_body(maud::html!(
            input type="hidden" name="identity_uuid" value=(identity_uuid) {}
            (bux::input::select::nilla::nilla_select("identity_type", "Type", &type_select_options, Some(&identity.identity_type.to_string())))
            (bux::input::text::string::name_label_value("name", "Name", Some(&identity.name)))
            (bux::input::text::string::name_label_value("email", "Email", match &identity.email {
                Some(email) => Some(email),
                None => None,
            }))
            (bux::input::textarea::string::name_label_value("note", "Note", match &identity.note {
                Some(note) => Some(note),
                None => None,
            }))
            (bux::input::text::string::name_label_value("avatar_uri", "Avatar URI", match &identity.avatar_uri {
                Some(uri) => Some(uri),
                None => None,
            }))
            (bux::input::checkbox::name_label_checked("active", "Active", identity.active))
        ));
        doc.add_body(maud::html! {
            identity-add-panel {
                section {
                    (panel)
                }
            }
        });

        Ok(Response::HTML(doc.into()))
    }
}
