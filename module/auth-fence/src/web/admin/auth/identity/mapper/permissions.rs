#[approck::http(GET /admin/auth/identity/{identity_uuid:Uuid}/permissions; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        let identity_uuid = path.identity_uuid;
        let identity_detail = crate::api::admin::identity::detail::detail::call(
            app,
            identity,
            crate::api::admin::identity::detail::detail::Input { identity_uuid },
        )
        .await?;

        doc.set_title("Assign Permissions");
        doc.add_css("/admin/auth/identity/mapper/permissions.css");
        doc.add_js("/admin/auth/identity/mapper/permissions.js");

        let permissions = match crate::api::admin::permission::list::list::call(
            app,
            crate::api::admin::permission::list::list::Input { keyword: None },
        )
        .await
        {
            Ok(output) => output.permissions,
            Err(e) => {
                return Err(granite::process_error!("Unable to load permissions").add_context(e));
            }
        };

        let identity_permission_list: Vec<granite::Uuid> =
            crate::api::admin::identity::permission::list::call(
                app,
                identity,
                crate::api::admin::identity::permission::list::Input { identity_uuid },
            )
            .await?
            .permissions
            .iter()
            .map(|perm| perm.permission_uuid)
            .collect();

        let identity_role_map = crate::api::admin::permission::map::map::call(
            app,
            identity,
            crate::api::admin::permission::map::map::Input {
                identity_uuid: Some(identity_uuid),
            },
        )
        .await?
        .permissions
        .into_iter()
        .map(|perm| (perm.permission_uuid, perm))
        .collect::<std::collections::HashMap<_, _>>();

        approck::info!("{:?}", &identity_role_map);

        let title = format!("Assign Permissions to Identity {}", identity_detail.name);
        let mut panel =
            bux::component::save_cancel_form_panel(&title, &identity_detail.ml_admin(""));
        panel.add_body(maud::html!(
            identity-permission-form {
                input type="hidden" name="identity_uuid" value=(identity_uuid) {}
                @for permission in permissions {
                    div {
                        @if let Some(perm) = identity_role_map.get(&permission.permission_uuid) {
                            (bux::input::checkbox::name_label_value_checked_help(
                                "permission_uuid",
                                &permission.name,
                                &permission.permission_uuid,
                                identity_permission_list.contains(&permission.permission_uuid),
                                format!(" (Granted via roles: {})", perm.roles.iter().map(|r| r.name.clone()).collect::<Vec<String>>().join(", ")).as_str(),
                            ))
                        } @else {
                            (bux::input::checkbox::name_label_value_checked(
                                "permission_uuid",
                                &permission.name,
                                &permission.permission_uuid,
                                identity_permission_list.contains(&permission.permission_uuid),
                            ))
                        }
                    }
                }
            }
        ));

        doc.add_body(maud::html! {
            section {
                (panel)
            }
        });

        Ok(Response::HTML(doc.into()))
    }
}
