use crate::{Markup, html};

/// Data for the primary navigation menu
/// Menu Links
/// - CSS class "selected" is applied to menu links and dropdown links if the current URI starts with the href
///

#[derive(Default)]
pub struct Nav1Data {
    menu_items: Vec<Nav1Item1>,
}

pub enum Nav1Item1 {
    Divider,
    Dropdown(Nav1Dropdown),
    Link(Nav1Link),
    Logo(Nav1Logo),
}

pub struct Nav1Dropdown {
    title: String,
    id: String,
    items: Vec<Nav1Item2>,
}

impl Nav1Dropdown {
    pub fn add_divider(&mut self) {
        self.items.push(Nav1Item2::Divider);
    }

    pub fn add_header(&mut self, title: &str) {
        self.items.push(Nav1Item2::Header(Nav1DropdownHeader {
            title: title.to_string(),
        }));
    }

    pub fn add_link(&mut self, title: &str, body: &str, href: &str, id: &str) {
        self.items.push(Nav1Item2::Link(Nav1DropdownLink {
            title: title.to_string(),
            body: body.to_string(),
            href: href.to_string(),
            id: id.to_string(),
        }));
    }

    pub fn add_paragraph(&mut self, title: &str, body: &str) {
        self.items.push(Nav1Item2::Paragraph(Nav1DropdownParagraph {
            title: title.to_string(),
            body: body.to_string(),
        }));
    }
}

pub struct Nav1Link {
    title: String,
    href: String,
    id: String,
}

pub struct Nav1Logo {
    url: String,
    alt: String,
}

pub enum Nav1Item2 {
    Divider,
    Header(Nav1DropdownHeader),
    Link(Nav1DropdownLink),
    Paragraph(Nav1DropdownParagraph),
}

pub struct Nav1DropdownHeader {
    title: String,
}

pub struct Nav1DropdownLink {
    title: String,
    body: String,
    href: String,
    id: String,
}

pub struct Nav1DropdownParagraph {
    title: String,
    body: String,
}

pub trait Nav1: super::Base {
    fn data(&self) -> &Nav1Data;
    fn data_mut(&mut self) -> &mut Nav1Data;

    fn add_nav1_logo(&mut self, url: &str, alt: &str) {
        Nav1::data_mut(self)
            .menu_items
            .push(Nav1Item1::Logo(Nav1Logo {
                url: url.to_string(),
                alt: alt.to_string(),
            }));
    }

    fn add_nav1_divider(&mut self) {
        Nav1::data_mut(self).menu_items.push(Nav1Item1::Divider);
    }

    fn add_nav1_menu_link(&mut self, title: &str, href: &str, id: &str) {
        Nav1::data_mut(self)
            .menu_items
            .push(Nav1Item1::Link(Nav1Link {
                title: title.to_string(),
                href: href.to_string(),
                id: id.to_string(),
            }));
    }

    fn add_nav1_menu_dropdown(&mut self, title: &str, id: &str) -> &mut Nav1Dropdown {
        Nav1::data_mut(self)
            .menu_items
            .push(Nav1Item1::Dropdown(Nav1Dropdown {
                title: title.to_string(),
                id: id.to_string(),
                items: Vec::new(),
            }));

        match Nav1::data_mut(self).menu_items.last_mut() {
            Some(Nav1Item1::Dropdown(dropdown)) => dropdown,
            _ => panic!("Failed to add dropdown menu"),
        }
    }

    fn render_nav1(&self) -> Markup {
        html! {
            nav aria-label="Primary Navigation" id="primary-navigation" {

                menu-wrapper-outer {

                    menu-wrapper-inner {
                        nav-header id="vertical-nav-header" {
                            a href=(self.logo_href()) aria-label=(format!("Home - {}", self.site_name())) {
                                img src=(self.logo_src()) alt=(format!("{} Logo", self.site_name())) {}
                            }
                        }
                        ul.nav-menu {
                            @for menu_group in super::Base::menu(self).iter_for_render() {
                                li.menu-group {
                                    @if let Some(heading) = &menu_group.heading {
                                        a class=(format!("group-name{}", if super::Base::data(self).uri == heading.uri { " selected" } else { "" })) href=(heading.uri) {
                                            (heading.label)
                                            @if let Some(name) = &heading.name {
                                                small { (name) }
                                            }
                                        }
                                    }
                                    ul {
                                        @for item in &menu_group.items {
                                            @match &item.inner {
                                                approck::MenuItemInner::Link { uri } => {
                                                    li {
                                                        @let icon = match &item.icon {
                                                            Some(icon) => html!{(icon) " "},
                                                            None => html! {},
                                                        };
                                                        a class=(if &super::Base::data(self).uri == uri { "selected" } else { "" }) href=(uri) { (icon) (item.label) }
                                                    }
                                                }
                                                approck::MenuItemInner::SubMenu { .. } => {
                                                    // This is the flyout

                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            @for item in &Nav1::data(self).menu_items {
                                @match item {
                                    Nav1Item1::Divider => {
                                        li.menu-item-divider {}
                                    }
                                    Nav1Item1::Dropdown (dropdown) => {
                                        (self.render_nav1_dropdown(dropdown))
                                    }
                                    Nav1Item1::Link (link) => {
                                        li.menu-item id=(link.id) {
                                            a class=(if super::Base::data(self).uri.starts_with(&link.href) { "menu-link selected" } else { "menu-link" }) href=(link.href) { (link.title) } }
                                    }
                                    Nav1Item1::Logo (logo) => {
                                        li.menu-item-logo { img src=(logo.url) alt=(logo.alt) {} }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // Adds a dropdown menu
    fn render_nav1_dropdown(&self, dropdown: &Nav1Dropdown) -> maud::Markup {
        let header_id = format!(
            "{}-dropdown-header",
            dropdown.title.replace(' ', "-").to_lowercase()
        );

        // 1. Create a variable to hold the markup for the items and one to indicate if the dropdown is selected
        let mut items_markup = Vec::with_capacity(dropdown.items.len());
        let mut dropdown_selected = false;

        // 2. Iterate on items and push the markup to the variable
        for item in &dropdown.items {
            match item {
                Nav1Item2::Divider => {
                    items_markup.push(html! { li.dropdown-menu-item-divider {} });
                }
                Nav1Item2::Header(header) => {
                    items_markup
                        .push(html! { li.dropdown-menu-item-header { h6 { (header.title) } } });
                }
                Nav1Item2::Link(link) => {
                    // 3. If one of the items is a link and the current URI starts with the href, add the selected class
                    let link_class = if super::Base::data(self).uri == link.href {
                        dropdown_selected = true;
                        "dropdown-menu-link selected"
                    } else {
                        "dropdown-menu-link"
                    };
                    items_markup.push(html! {
                        li.dropdown-menu-item {
                            a class=(link_class) href=(link.href) id=(link.id) { (link.title) }
                            @if !link.body.is_empty() {
                                span { (link.body) }
                            }
                        }
                    });
                }
                Nav1Item2::Paragraph(paragraph) => {
                    items_markup.push(html! { li.dropdown-menu-item-paragraph {
                        h6 { (paragraph.title) } }
                        p { (paragraph.body) }
                    });
                }
            }
        }

        // 4. Set the dropdown selected variable
        let dropdown_class = if dropdown_selected {
            "dropdown-menu-toggle selected"
        } else {
            "dropdown-menu-toggle"
        };

        // 5. Render the dropdown with the selected class and the items
        html! {
            li class="menu-item-dropdown" id=(dropdown.id) {
                a class=(dropdown_class) href="#" aria-haspopup="menu" role="button" aria-expanded="false" data-toggle="dropdown" {
                    (dropdown.title)
                }
                ul.dropdown-menu-list aria-labelledby=(header_id) {
                    li.dropdown-menu-item id=(header_id) { // Dynamic id used to label the dropdown menu
                        dropdown-header {
                                h5 {(dropdown.title)} // Use the passed label
                                a href="" role="button" aria-label="Close Dropdown Menu" {
                                    i.far.fa-times-circle aria-hidden="true" {}
                                }
                        }
                    }
                    @for item in &items_markup {
                        (item)
                    }
                }
            }
        }
    }
}
