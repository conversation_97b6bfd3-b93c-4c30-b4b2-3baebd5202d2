mod cliffy;
mod footer_links;
mod footer_social;
mod nav1;
mod nav2;
mod page_nav;

pub use cliffy::{<PERSON>y, CliffyData};
pub use footer_links::{FooterLinks, FooterLinksData};
pub use footer_social::{FooterSocial, FooterSocialData};
use maud::html;
pub use nav1::{Nav1, Nav1Data};
pub use nav2::{Nav2, Nav2Data, Nav2Dropdown, Nav2Icon, Nav2Logo};
pub use page_nav::{PageNav, PageNavData};

#[derive(Default)]
pub struct BaseData {
    uri: String,
    title: Option<String>,
    site_name: Option<String>,
    head: Vec<maud::Markup>,
    menu: approck::Menu,
    body: Vec<maud::Markup>,
    body_class: Option<String>,
    body_display: BodyDisplay,
    headerbar: Vec<maud::Markup>,
    sidebar: Vec<maud::Markup>,
    tail: Vec<maud::Markup>,
    js_list: Vec<String>,
    css_list: Vec<String>,
    status: approck::StatusCode,
    script_list: Vec<String>,
    script_json: Option<granite::JsonValue>,
    style_list: Vec<String>,
    header_map: approck::HeaderMap,

    // href, src
    logo: Option<Logo>,
    app_js_bundle_uri: Option<String>,
    app_css_bundle_uri: Option<String>,
    document_js_bundle_uri: Option<String>,
    document_css_bundle_uri: Option<String>,
    handler_js_bundle_uri: Option<String>,
    handler_css_bundle_uri: Option<String>,
}

#[derive(Default)]
pub enum BodyDisplay {
    Fluid,
    #[default]
    Container,
    Narrow,
    Plain,
    Fixed,
}

pub struct Logo {
    href: String,
    src: String,
}

pub trait Base: Into<approck::server::response::HTML> {
    fn data(&self) -> &BaseData;
    fn data_mut(&mut self) -> &mut BaseData;
    fn data_owned(self) -> BaseData;

    fn site_name(&self) -> &str {
        self.data()
            .site_name
            .as_deref()
            .unwrap_or("[DEFAULT SITE NAME]")
    }

    fn set_site_name(&mut self, name: &str) {
        self.data_mut().site_name = Some(name.to_string());
    }

    fn logo_src(&self) -> &str {
        match &self.data().logo {
            Some(custom_logo) => custom_logo.src.as_str(),
            None => {
                "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48Y2lyY2xlIGN4PSIyNSIgY3k9IjI1IiByPSIyMCIgZmlsbD0idXJsKCNncmFkKSIvPjxkZWZzPjxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZCIgeDE9IjAiIHkxPSIwIiB4Mj0iMSIgeTI9IjEiPjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiM0N0U1RkYiLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiM4NDJFRkYiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48L3N2Zz4="
            }
        }
    }

    fn logo_href(&self) -> &str {
        match &self.data().logo {
            Some(custom_logo) => custom_logo.href.as_str(),
            None => "/", // Default logo URL
        }
    }

    fn add_logo(&mut self, href: &str, src: &str) {
        self.data_mut().logo = Some(Logo {
            href: href.to_string(),
            src: src.to_string(),
        });
    }

    fn menu_mut(&mut self) -> &mut approck::Menu {
        &mut self.data_mut().menu
    }
    fn menu(&self) -> &approck::Menu {
        &self.data().menu
    }
    fn menu_replace(&mut self, menu: approck::Menu) {
        self.data_mut().menu = menu;
    }

    fn set_app_bundle(&mut self, js_bundle_uri: Option<&str>, css_bundle_uri: Option<&str>) {
        let data = self.data_mut();
        data.app_js_bundle_uri = js_bundle_uri.map(|v| v.to_string());
        data.app_css_bundle_uri = css_bundle_uri.map(|v| v.to_string());
    }

    fn set_document_bundle(&mut self, js_bundle_uri: Option<&str>, css_bundle_uri: Option<&str>) {
        let data = self.data_mut();
        data.document_js_bundle_uri = js_bundle_uri.map(|v| v.to_string());
        data.document_css_bundle_uri = css_bundle_uri.map(|v| v.to_string());
    }

    fn set_handler_bundle(&mut self, js_bundle_uri: Option<&str>, css_bundle_uri: Option<&str>) {
        let data = self.data_mut();
        data.handler_js_bundle_uri = js_bundle_uri.map(|v| v.to_string());
        data.handler_css_bundle_uri = css_bundle_uri.map(|v| v.to_string());
    }

    fn set_body_class(&mut self, class_name: &str) {
        self.data_mut().body_class = Some(class_name.to_string());
    }

    fn set_body_display_fluid(&mut self) {
        self.data_mut().body_display = BodyDisplay::Fluid;
    }

    fn set_body_display_container(&mut self) {
        self.data_mut().body_display = BodyDisplay::Container;
    }

    fn set_body_display_plain(&mut self) {
        self.data_mut().body_display = BodyDisplay::Plain;
    }

    fn set_body_display_fixed(&mut self) {
        self.data_mut().body_display = BodyDisplay::Fixed;
    }

    fn add_error(&mut self, error: &granite::Error) {
        let error_type = error.get_error_type();
        let error_uuid = error.get_error_uuid();

        self.add_body(html!(
            div style="text-align: center; margin-top: 2em;" {
                h1 {
                    "Oh no! An unexpected error..."
                }
                hr;
                div style="display: grid; grid-template-columns: auto auto; gap: 0.5rem; max-width: 640px; margin: 0 auto; text-align: left;" {
                    div { b { "type: " } }
                    div { code { (error_type) } }

                    div { b { "error: " } }
                    div { code { (error_uuid) } }

                    @match error.get_request_uuid() {
                        Some(request_uuid) => {
                            div { b { "request: " } }
                            div { code { (request_uuid) } }
                        }
                        None => {}
                    }

                    @match error.get_uri() {
                        Some(uri) => {
                            div { b { "webpage: " } }
                            div { code { (uri) } }
                        }
                        None => {}
                    }

                    @match (error.get_code(), error.get_external_message()) {
                        (Some(code), Some(message)) => {
                            div { b { "message: " } }
                            div { code { (code) } " " (message) }
                        }
                        (Some(code), None) => {
                            div { b { "message: " } }
                            div { code { (code) } }
                        }
                        (None, Some(message)) => {
                            div { b { "message: " } }
                            div { (message) }
                        }
                        (None, None) => {}
                    }
                }
                hr;
                p {
                    "This error has been recorded.  If additional help is needed, please reference the information above when contacting support."
                }
            }
        ))
    }
    fn set_title(&mut self, title: &str) {
        self.data_mut().title = Some(title.to_string());
    }
    fn set_uri(&mut self, uri: &str) {
        self.data_mut().uri = uri.to_string();
    }
    fn add_head(&mut self, head: maud::Markup) {
        self.data_mut().head.push(head);
    }
    fn add_body(&mut self, body: maud::Markup) {
        self.data_mut().body.push(body);
    }
    fn add_headerbar(&mut self, content: maud::Markup) {
        self.data_mut().headerbar.push(content);
    }
    fn add_sidebar(&mut self, sidebar: maud::Markup) {
        self.data_mut().sidebar.push(sidebar);
    }

    fn add_component(&mut self) {}

    fn body_iter(&self) -> std::slice::Iter<maud::Markup> {
        self.data().body.iter()
    }
    fn headerbar_iter(&self) -> std::slice::Iter<maud::Markup> {
        self.data().headerbar.iter()
    }
    fn is_headerbar_empty(&self) -> bool {
        self.headerbar_iter().count() == 0
    }
    fn sidebar_iter(&self) -> std::slice::Iter<maud::Markup> {
        self.data().sidebar.iter()
    }
    fn is_sidebar_empty(&self) -> bool {
        self.sidebar_iter().count() == 0
    }

    /// Returns an iterator over all JS bundle URIs and the js_list in a specific order
    fn js_list_iter(&self) -> impl Iterator<Item = &String> {
        let data = self.data();
        let app_js_bundle_uri = data.app_js_bundle_uri.as_ref();
        let document_js_bundle_uri = data.document_js_bundle_uri.as_ref();
        let handler_js_bundle_uri = data.handler_js_bundle_uri.as_ref();
        let js_list = &data.js_list;

        app_js_bundle_uri
            .into_iter()
            .chain(document_js_bundle_uri)
            .chain(handler_js_bundle_uri)
            .chain(js_list.iter())
    }

    /// Returns an iterator over all CSS bundle URIs and the css_list in a specific order
    fn css_list_iter(&self) -> impl Iterator<Item = &String> {
        let data = self.data();
        let app_css_bundle_uri = data.app_css_bundle_uri.as_ref();
        let document_css_bundle_uri = data.document_css_bundle_uri.as_ref();
        let handler_css_bundle_uri = data.handler_css_bundle_uri.as_ref();
        let css_list = &data.css_list;

        app_css_bundle_uri
            .into_iter()
            .chain(document_css_bundle_uri)
            .chain(handler_css_bundle_uri)
            .chain(css_list.iter())
    }

    /// This is to be used to produce a single chunk of markup for the body
    fn render_body_inner(&self) -> maud::Markup {
        maud::html!(
            @for chunk in Base::body_iter(self) {
                (chunk)
            }
        )
    }
    fn render_headerbar_inner(&self) -> maud::Markup {
        maud::html! {
            @for content in self.headerbar_iter() {
                (content)
            }
        }
    }
    fn render_sidebar_inner(&self) -> maud::Markup {
        maud::html!(
            @for chunk in Base::sidebar_iter(self) {
                (chunk)
            }
        )
    }
    fn add_tail(&mut self, tail: maud::Markup) {
        self.data_mut().tail.push(tail);
    }
    fn add_js(&mut self, js: &str) {
        self.data_mut().js_list.push(js.to_string());
    }
    fn add_css(&mut self, css: &str) {
        self.data_mut().css_list.push(css.to_string());
    }
    fn set_status(&mut self, status: approck::StatusCode) {
        self.data_mut().status = status;
    }
    fn set_status_ok(&mut self) {
        self.data_mut().status = approck::StatusCode::OK;
    }
    fn set_status_307(&mut self) {
        self.data_mut().status = approck::StatusCode::TEMPORARY_REDIRECT;
    }
    fn set_status_308(&mut self) {
        self.data_mut().status = approck::StatusCode::PERMANENT_REDIRECT;
    }
    fn set_status_404(&mut self) {
        self.data_mut().status = approck::StatusCode::NOT_FOUND;
    }
    fn set_status_500(&mut self) {
        self.data_mut().status = approck::StatusCode::INTERNAL_SERVER_ERROR;
    }
    fn add_script(&mut self, script: &str) {
        self.data_mut().script_list.push(script.to_string());
    }
    fn set_script_json(&mut self, data: granite::JsonValue) {
        self.data_mut().script_json = Some(data);
    }
    fn clear_script_json(&mut self) {
        self.data_mut().script_json = None;
    }
    fn add_style(&mut self, style: &str) {
        self.data_mut().style_list.push(style.to_string());
    }

    fn into_response_html(self) -> approck::server::response::HTML
    where
        Self: Sized,
    {
        let text = self.render().into_string();
        let data = self.data_owned();
        let status = data.status;
        let mut header_map = data.header_map;

        // if no cache-control header is set, set it to no-cache
        if !header_map.contains_key(http::header::CACHE_CONTROL) {
            header_map.insert(
                http::header::CACHE_CONTROL,
                http::HeaderValue::from_static("no-cache, no-store, private"),
            );
        }

        approck::server::response::HTML::new_with_status_and_header_map(text, status, header_map)
    }

    fn render(&self) -> maud::Markup {
        let data = self.data();

        let body_class = self.data().body_class.as_deref().unwrap_or("");

        // Prevent xss attacks in embedded data.
        let safe_script_data = data.script_json.as_ref().map(|v| {
            let json_str = v.to_string();
            json_str.replace("</script>", r"<\/script>")
        });

        #[rustfmt::skip]
        return maud::html! {
            (maud::DOCTYPE)
            html {
                head {
                    meta charset="utf-8";
                    meta name="viewport" content="width=device-width, initial-scale=1";
                    // FontAwesome
                    link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet";

                    @for module in Base::js_list_iter(self) {
                        link rel="modulepreload" href=(module) {}
                    }

                    @for css in Base::css_list_iter(self) {
                        link href=(css) rel="stylesheet" {}
                    }

                    @if let Some(title) = &data.title {
                        title { (title) }
                    }

                    @for chunk in &data.head {
                        (chunk)
                    }

                    @for style in &data.style_list {
                        style { (style) }
                    }
                }
                body class=(body_class) {
                    (self.render_body())

                    div #bux-document-exception { }

                    // This must come above script includes so it is present always
                    @if let Some(safe_script_data) = safe_script_data {
                        script type="module" {
                            (maud::PreEscaped(format!("globalThis.script_data = {safe_script_data};")))
                        }
                    }

                    @for module in Base::js_list_iter(self) {
                        script type="module" src=(module) {}
                    }

                    @for script in &data.script_list {
                        script type="module" { (maud::PreEscaped(script)) }
                    }
                }
            }
        };
    }

    fn render_body(&self) -> maud::Markup {
        #[rustfmt::skip]
        return maud::html! {
            (self.render_body_inner())
        };
    }
}
