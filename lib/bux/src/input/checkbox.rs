use crate::{Mark<PERSON>, html};

pub fn name_value(name: &str, checked: bool) -> Markup {
    html! {
        bux-input-checkbox name=(name) checked=(checked) {}
    }
}

pub fn name_label_checked(name: &str, label: &str, checked: bool) -> Markup {
    html! {
        bux-input-checkbox name=(name) checked=(checked) {
            (label)
        }
    }
}

pub fn name_label_value_checked(
    name: &str,
    label: &str,
    value: &impl std::fmt::Display,
    checked: bool,
) -> Markup {
    html! {
        bux-input-checkbox name=(name) value=(value) checked=(checked) {
            (label)
        }
    }
}

pub fn name_label_checked_help(name: &str, label: &str, checked: bool, help: &str) -> Markup {
    html! {
        bux-input-checkbox name=(name) checked=(checked) help=(help) {
            (label)
        }
    }
}

pub fn name_label_value_checked_help(
    name: &str,
    label: &str,
    value: &impl std::fmt::Display,
    checked: bool,
    help: &str,
) -> Markup {
    html! {
        bux-input-checkbox name=(name) value=(value) checked=(checked) help=(help) {
            (label)
        }
    }
}
