import "./password_creator.mcss";
import "@crate/input/text/password.mts";
import { SEC } from "@granite/lib.mts";
import { BuxInputTextPassword } from "@crate/input/text/password.mts";
import { password_analysis } from "@granite/password.mts";

class BuxComponentPasswordCreator extends HTMLElement {
    private $password = SEC(BuxInputTextPassword, this, "bux-input-text-password[name=password]");
    private $confirm = SEC(
        BuxInputTextPassword,
        this,
        "bux-input-text-password[name=confirm_password]",
    );
    private $meter = SEC(HTMLMeterElement, this, "meter");
    private $status = SEC(HTMLOutputElement, this, "output");

    constructor() {
        super();
        this.refresh();
    }

    connectedCallback() {
        this.$password.addEventListener("input", () => this.refresh());
        this.$confirm.addEventListener("input", () => this.refresh());
        this.$confirm.addEventListener("change", () => this.validate());
    }

    disconnectedCallback() {
        this.$password.removeEventListener("input", () => this.refresh());
        this.$confirm.removeEventListener("input", () => this.refresh());
        this.$confirm.removeEventListener("change", () => this.validate());
    }

    // Update the meter and status
    private refresh() {
        const analysis = password_analysis(this.value ?? "");
        this.$meter.value = analysis.meter_value;
        this.$status.textContent = analysis.meter_text;

        // Clear any previous errors when user types
        this.$confirm.set_e(undefined);
    }

    // Public API
    get value(): string | undefined {
        return this.$password.value || undefined;
    }

    set_e(value: string | undefined) {
        this.$password.set_e(value);
    }

    set_confirm_e(value: string | undefined) {
        this.$confirm.set_e(value);
    }

    // Validate passwords match and show error if they don't
    public validate(): boolean {
        const p1 = this.$password.value;
        const p2 = this.$confirm.value;

        this.$password.set_e(undefined);
        this.$confirm.set_e(undefined);

        if (p1 && p2) {
            const analysis = password_analysis(p1);
            if (!analysis.acceptable) {
                this.set_e("Password does not meet strength requirements");
                return false;
            }

            if (p1 !== p2) {
                this.set_confirm_e("Passwords do not match");
                return false;
            }

            return true;
        }

        if (!p1) {
            this.set_e("Password is required");
        }

        if (!p2) {
            this.set_confirm_e("Please confirm your password");
        }

        return false;
    }
}

customElements.define("bux-component-password-creator", BuxComponentPasswordCreator);
export default BuxComponentPasswordCreator;
