use crate::{<PERSON><PERSON>, <PERSON><PERSON>, html};

pub struct VerificationCode<'a> {
    pub contact_type: ContactType<'a>,
    pub element_id: Option<&'a str>,
    pub code_length: usize,
    pub is_verified: bool,
    pub user_error: Option<&'a str>,
    pub resend_href: Option<&'a str>,
}

#[derive(<PERSON><PERSON>, Co<PERSON>)]
pub enum ContactType<'a> {
    Phone(&'a str),
    Email(&'a str),
}

pub fn new_email_4<'a>(email: &'a str) -> VerificationCode<'a> {
    VerificationCode {
        contact_type: ContactType::<PERSON>ail(email),
        element_id: None,
        code_length: 4,
        is_verified: false,
        user_error: None,
        resend_href: None,
    }
}

pub fn new_phone_4<'a>(phone: &'a str) -> VerificationCode<'a> {
    VerificationCode {
        contact_type: ContactType::Phone(phone),
        element_id: None,
        code_length: 4,
        is_verified: false,
        user_error: None,
        resend_href: None,
    }
}

impl<'a> VerificationCode<'a> {
    pub fn set_code_length(&mut self, length: usize) {
        self.code_length = length;
    }

    pub fn set_id(&mut self, id: &'a str) {
        self.element_id = Some(id);
    }

    pub fn set_resend_href(&mut self, link: &'a str) {
        self.resend_href = Some(link);
    }

    pub fn set_user_error(&mut self, error: &'a str) {
        self.user_error = Some(error);
    }

    pub fn set_verified(&mut self, is_verified: bool) {
        self.is_verified = is_verified;
    }

    fn get_title(&self) -> &str {
        match self.contact_type {
            ContactType::Phone(_) => "Phone Verification",
            ContactType::Email(_) => "Email Verification",
        }
    }

    fn get_description(&self) -> String {
        match (self.contact_type, self.is_verified) {
            (ContactType::Phone(_), true) => "Phone is verified!".to_string(),
            (ContactType::Email(_), true) => "Email is verified!".to_string(),
            (ContactType::Phone(_), false) => format!(
                "Enter the {}-digit code sent to your phone:",
                self.code_length
            ),
            (ContactType::Email(_), false) => {
                format!(
                    "Enter the {}-digit code sent to your email address:",
                    self.code_length
                )
            }
        }
    }
}

impl Render for VerificationCode<'_> {
    fn render(&self) -> Markup {
        let description = self.get_description();
        let contact_type_attr = match self.contact_type {
            ContactType::Phone(_) => "Phone",
            ContactType::Email(_) => "Email",
        };

        html! {
            bux-component-verification-code id=[self.element_id] code-length=(self.code_length) contact-type=(contact_type_attr) {
                h2.x-title { (self.get_title()) }

                p.x-target {
                    @match self.contact_type {
                        ContactType::Phone(phone) => { (phone) }
                        ContactType::Email(email) => { (email) }
                    }
                }

                @if let Some(error) = self.user_error {
                    p.x-error-wrapper {
                        (error)
                    }
                }

                @if self.is_verified {
                    div.x-success-wrapper {
                        div {
                            i.fas.fa-check-circle.verified-checkmark {}
                        }
                        p { "Verified Successfully!" }
                    }
                } @else {
                    p.x-description {
                        (description)
                    }

                    div.x-input-wrapper {
                        input.x-code pattern=(format!("^[0-9]{}$", self.code_length)) type="text" name="verification_code" placeholder="Enter Code" {}
                    }

                    p.x-resend-wrapper {
                        "Didn't get a code? "
                        a href=(self.resend_href.unwrap_or("#")) { "Resend Code Now" }
                    }
                }
            }
        }
    }
}
