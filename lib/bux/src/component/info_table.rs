use crate::{<PERSON><PERSON>, <PERSON><PERSON>, html};
use std::str::FromStr;

pub enum Df4lStatus {
    Active(String), // e.g., "Active As Of Nov 15, 2023"
    HasDataButNotEnrolled,
    NotEnrolled,
}

pub struct InfoTable<'a, T: 'a> {
    pub heading: &'a str,
    pub rows: Vec<Row<'a, T>>, // List of rows
    pub entity: T,             // Single entity instead of a list of entities
    pub form_action: Option<&'a str>,
}
pub struct Row<'a, T: 'a> {
    pub header: &'a str,
    renderer: Box<dyn Fn(&T) -> Markup + 'a>,
}

impl FromStr for Df4lStatus {
    type Err = ();

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        if s.starts_with("Active As Of ") {
            Ok(Df4lStatus::Active(s.to_string()))
        } else if s == "Has Data But Not Enrolled" {
            Ok(Df4lStatus::HasDataButNotEnrolled)
        } else {
            Ok(Df4lStatus::NotEnrolled)
        }
    }
}

impl<'a, T> Row<'a, T> {
    pub fn new(header: &'a str, renderer: impl Fn(&T) -> Markup + 'a) -> Self {
        Self {
            header,
            renderer: Box::new(renderer),
        }
    }

    pub fn render_cell(&self, entity: &T) -> Markup {
        (self.renderer)(entity)
    }
}

impl<'a, T> InfoTable<'a, T> {
    // Adds a row with a header and renderer function for the entity
    pub fn add_row(&mut self, header: &'a str, renderer: impl Fn(&T) -> Markup + 'a) {
        self.rows.push(Row::new(header, renderer));
    }

    // Adds a name row
    pub fn add_name_row<F>(&mut self, get_value: F)
    where
        F: for<'b> Fn(&'b T) -> &'b str + 'a,
    {
        self.add_row("Name:", move |entity| html! { (get_value(entity)) });
    }

    pub fn add_identifer_row<F>(&mut self, label: &'a str, get_value: F)
    where
        F: for<'b> Fn(&'b T) -> &'b str + 'a,
    {
        self.add_row(label, move |entity| html! { code {(get_value(entity))} });
    }

    // Adds an email row
    pub fn add_email_row<F>(&mut self, get_value: F)
    where
        F: for<'b> Fn(&'b T) -> &'b str + 'a,
    {
        self.add_row("Email:", move |entity| html! { (get_value(entity)) });
    }

    pub fn add_create_ts<F>(&mut self, get_ts: F)
    where
        F: for<'b> Fn(&'b T) -> granite::DateTimeUtc + 'a,
    {
        self.add_row("Created On:", move |entity| {
            let ts = get_ts(entity);
            html! {
                (ts.format("%b %d, %Y %I:%M %p"))
            }
        });
    }

    pub fn add_note_row<F>(&mut self, get_value: F)
    where
        F: for<'b> Fn(&'b T) -> &'b str + 'a,
    {
        self.add_row("Note:", move |entity| html! { (get_value(entity)) });
    }

    // Adds a status row with dynamic header and colored labels for Active/Inactive
    pub fn add_status_row<F>(&mut self, header: &'a str, get_value: F)
    where
        F: for<'b> Fn(&'b T) -> &'b str + 'a,
    {
        self.add_row(header, move |entity| {
            let status = get_value(entity).to_lowercase();
            html! {
                @if status == "active" {
                    label-tag.primary { "Active" }
                } @else {
                    label-tag.default { "Inactive" }
                }
            }
        });
    }

    // Adds a "DF4L" (Debt2Capital™) column with different label colors based on status
    pub fn add_df4l_status<F>(&mut self, header: &'a str, get_status: F)
    where
        F: for<'b> Fn(&'b T) -> Df4lStatus + 'a,
    {
        self.add_row(header, move |entity| {
            let status = get_status(entity);
            html! {
                @match status {
                    Df4lStatus::Active(ref s) => label-tag.primary { (s) },
                    Df4lStatus::HasDataButNotEnrolled => label-tag.warning { "Has Data But Not Enrolled" },
                    Df4lStatus::NotEnrolled => label-tag.default { "Not Enrolled" },
                }
            }
        });
    }

    // Adds a status row with dynamic header and colored labels for Active/Inactive
    pub fn add_active_status_row<F>(&mut self, header: &'a str, get_value: F)
    where
        F: for<'b> Fn(&'b T) -> bool + 'a,
    {
        self.add_row(header, move |entity| {
            let is_active = get_value(entity);
            html! {
                @if is_active {
                    label-tag.primary { "Active" }
                } @else if !is_active {
                    label-tag.default { "Inactive" }
                } @else {
                    label-tag.warning { "Invalid Status" }
                }
            }
        });
    }

    // Adds a row with a clickable link using dynamic label and href
    pub fn add_link_row<F1, F2>(&mut self, header: &'a str, get_href: F1, get_label: F2)
    where
        F1: for<'b> Fn(&'b T) -> String + 'a,
        F2: for<'b> Fn(&'b T) -> String + 'a,
    {
        self.add_row(header, move |entity| {
            let href = get_href(entity);
            let label = get_label(entity);
            html! {
                a href=(href) {
                    (label)
                }
            }
        });
    }

    // Set the heading for the table
    pub fn set_heading(&mut self, heading: &'a str) {
        self.heading = heading;
    }
}

impl<T> Render for InfoTable<'_, T> {
    fn render(&self) -> Markup {
        html! {
            bux-component-info-table {
                h2 { (self.heading) }

                table-wrapper.data-list {
                    table {
                        @for row in &self.rows {
                            tr {
                                th { (row.header) }
                                td { (row.render_cell(&self.entity)) }
                            }
                        }
                    }
                }
            }
        }
    }
}

// New `info_table` function for initializing an InfoTable with a single entity
pub fn info_table<'a, T>(entity: T) -> InfoTable<'a, T> {
    InfoTable {
        heading: "Details",
        rows: Vec::new(),
        entity,
        form_action: None,
    }
}
