use crate::{<PERSON><PERSON>, <PERSON><PERSON>, html};
use std::borrow::Cow;

use super::detail_table::{Filter, KeywordFilter, SelectFilter};

pub struct MultiHeaderTable<'a, T: 'a> {
    pub heading: Option<Cow<'a, str>>,
    pub subheading: Option<Cow<'a, str>>,
    pub header_rows: Vec<Vec<HeaderCell<'a>>>,
    pub columns: Vec<Column<'a, T>>,
    pub entities: Vec<T>,
    pub filters: Vec<Box<dyn Filter<'a> + 'a>>,
    pub form_action: Option<Cow<'a, str>>,
}

pub struct HeaderCell<'a> {
    pub label: Cow<'a, str>,
    pub colspan: usize,
}

impl<'a> HeaderCell<'a> {
    pub fn new(label: impl Into<Cow<'a, str>>, colspan: usize) -> Self {
        Self {
            label: label.into(),
            colspan,
        }
    }
}

pub struct Column<'a, T: 'a> {
    renderer: Box<dyn Fn(&T) -> Markup + 'a>,
    cell_class: Option<Cow<'a, str>>,
}

impl<'a, T> Column<'a, T> {
    pub fn new(renderer: impl Fn(&T) -> Markup + 'a) -> Self {
        Self {
            renderer: Box::new(renderer),
            cell_class: None,
        }
    }

    pub fn set_cell_class(mut self, class: impl Into<Cow<'a, str>>) -> Self {
        self.cell_class = Some(class.into());
        self
    }

    pub fn render_cell(&self, entity: &T) -> Markup {
        (self.renderer)(entity)
    }

    pub fn get_cell_class(&self) -> Option<&str> {
        self.cell_class.as_deref()
    }
}

pub fn multi_header_table<'a, T>(entities: impl IntoIterator<Item = T>) -> MultiHeaderTable<'a, T> {
    MultiHeaderTable {
        heading: None,
        subheading: None,
        header_rows: Vec::new(),
        columns: Vec::new(),
        entities: entities.into_iter().collect(),
        filters: Vec::new(),
        form_action: None,
    }
}

impl<'a, T> MultiHeaderTable<'a, T> {
    pub fn set_heading(&mut self, heading: impl Into<Cow<'a, str>>) {
        self.heading = Some(heading.into());
    }

    pub fn set_subheading(&mut self, subheading: impl Into<Cow<'a, str>>) {
        self.subheading = Some(subheading.into());
    }

    pub fn set_form_action(&mut self, action: impl Into<Cow<'a, str>>) {
        self.form_action = Some(action.into());
    }

    pub fn add_header_row(&mut self, row: Vec<HeaderCell<'a>>) {
        self.header_rows.push(row);
    }

    pub fn add_column(&mut self, renderer: impl Fn(&T) -> Markup + 'a) {
        self.columns.push(Column::new(renderer));
    }

    pub fn add_column_with_class(
        &mut self,
        renderer: impl Fn(&T) -> Markup + 'a,
        class: impl Into<Cow<'a, str>>,
    ) {
        self.columns
            .push(Column::new(renderer).set_cell_class(class.into()));
    }

    pub fn add_entity(&mut self, entity: T) {
        self.entities.push(entity);
    }

    pub fn add_entities<I>(&mut self, entities: I)
    where
        I: IntoIterator<Item = T>,
    {
        self.entities.extend(entities);
    }

    pub fn add_keyword_filter(&mut self, value: Option<&'a str>) -> &mut Self {
        self.filters.push(Box::new(KeywordFilter::new(
            "Keyword Search",
            "keyword",
            value,
        )));
        self
    }

    pub fn add_select_filter(
        &mut self,
        label: &'a str,
        param_name: &'a str,
        options: Vec<(&'a str, &'a str)>,
        value: Option<&'a str>,
    ) -> &mut Self {
        self.filters.push(Box::new(SelectFilter::new(
            label, param_name, options, value,
        )));
        self
    }
}

impl<T> Render for MultiHeaderTable<'_, T> {
    fn render(&self) -> Markup {
        html! {
            bux-component-multi-header-table {
                @if let Some(heading) = &self.heading {
                    h2 { (heading) }
                }
                @if let Some(subheading) = &self.subheading {
                    p { (subheading) }
                }

                @if !self.filters.is_empty() {
                    panel.filter-panel {
                        content {
                            form method="GET" action=(self.form_action.as_deref().unwrap_or("")) {
                                grid-3 {
                                    @for filter in &self.filters {
                                        (filter.render())
                                    }

                                    div.filter-actions {
                                        button.primary type="submit" {
                                            i.fas.fa-filter {} " Filter"
                                        }
                                        " "
                                        a.btn.secondary href=(self.form_action.as_deref().unwrap_or("")) {
                                            i.fas.fa-undo {} " Reset"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                table-wrapper.data-list {
                    table {
                        thead {
                            @for row in &self.header_rows {
                                tr {
                                    @for cell in row {
                                        th colspan=(cell.colspan) { (cell.label) }
                                    }
                                }
                            }
                        }
                        tbody {
                            @for entity in &self.entities {
                                tr {
                                    @for column in &self.columns {
                                        @if let Some(class) = column.get_cell_class() {
                                            td.(class) { (column.render_cell(entity)) }
                                        } @else {
                                            td { (column.render_cell(entity)) }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
