use crate::{<PERSON><PERSON>, <PERSON><PERSON>, html};
use std::borrow::Cow;
use std::str::FromStr;

#[derive(Debug, Clone, PartialEq, Eq)]
pub enum Df4lStatus {
    Active(String), // e.g., "Active As Of Nov 15, 2023"
    HasDataButNotEnrolled,
    NotEnrolled,
}

/// Holds configuration for a single action button
pub struct ButtonConfig<'a> {
    pub name: &'a str,
    pub icon: &'a str,
    pub color: &'a str,
}

impl FromStr for Df4lStatus {
    type Err = ();

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        if s.starts_with("Active As Of ") {
            Ok(Df4lStatus::Active(s.to_string()))
        } else if s == "Has Data But Not Enrolled" {
            Ok(Df4lStatus::HasDataButNotEnrolled)
        } else {
            Ok(Df4lStatus::NotEnrolled)
        }
    }
}
pub struct DetailTable<'a, T: 'a> {
    pub heading: &'a str,
    pub subheading: Option<&'a str>,
    pub columns: Vec<Column<'a, T>>,
    pub entities: Vec<T>,
    pub filters: Vec<Box<dyn Filter<'a> + 'a>>,
    pub form_action: Option<&'a str>,
}

pub struct Column<'a, T: 'a> {
    pub header: &'a str,
    renderer: Box<dyn Fn(&T) -> Markup + 'a>,
}

impl<'a, T> Column<'a, T> {
    pub fn new(header: &'a str, renderer: impl Fn(&T) -> Markup + 'a) -> Self {
        Self {
            header,
            renderer: Box::new(renderer),
        }
    }

    pub fn render_cell(&self, entity: &T) -> Markup {
        (self.renderer)(entity)
    }
}

pub trait Filter<'a> {
    fn label(&self) -> &'a str;
    fn render(&self) -> Markup;
    fn param_name(&self) -> &'a str;
    fn value(&self) -> Option<&'a str>;
}

pub struct KeywordFilter<'a> {
    label: &'a str,
    param_name: &'a str,
    value: Option<&'a str>,
}

impl<'a> KeywordFilter<'a> {
    pub fn new(label: &'a str, param_name: &'a str, value: Option<&'a str>) -> Self {
        Self {
            label,
            param_name,
            value,
        }
    }
}

impl<'a> Filter<'a> for KeywordFilter<'a> {
    fn label(&self) -> &'a str {
        self.label
    }

    fn render(&self) -> Markup {
        html! {
            (crate::input::text::string::name_label_value(
                self.param_name,
                self.label,
                self.value
            ))
        }
    }

    fn param_name(&self) -> &'a str {
        self.param_name
    }

    fn value(&self) -> Option<&'a str> {
        self.value
    }
}

pub struct SelectFilter<'a> {
    label: &'a str,
    param_name: &'a str,
    options: Vec<(&'a str, &'a str)>,
    value: Option<&'a str>,
}

impl<'a> SelectFilter<'a> {
    pub fn new(
        label: &'a str,
        param_name: &'a str,
        options: Vec<(&'a str, &'a str)>,
        value: Option<&'a str>,
    ) -> Self {
        Self {
            label,
            param_name,
            options,
            value,
        }
    }
}

impl<'a> Filter<'a> for SelectFilter<'a> {
    fn label(&self) -> &'a str {
        self.label
    }

    fn render(&self) -> Markup {
        html! {
            (crate::input::select::nilla::nilla_select(
                self.param_name,
                self.label,
                &self.options,
                self.value
            ))
        }
    }

    fn param_name(&self) -> &'a str {
        self.param_name
    }

    fn value(&self) -> Option<&'a str> {
        self.value
    }
}

pub struct CustomFilter<'a> {
    label: &'a str,
    param_name: &'a str,
    value: Option<&'a str>,
    renderer: Box<dyn Fn() -> Markup + 'a>,
}

impl<'a> CustomFilter<'a> {
    pub fn new(
        label: &'a str,
        param_name: &'a str,
        value: Option<&'a str>,
        renderer: impl Fn() -> Markup + 'a,
    ) -> Self {
        Self {
            label,
            param_name,
            value,
            renderer: Box::new(renderer),
        }
    }
}

impl<'a> Filter<'a> for CustomFilter<'a> {
    fn label(&self) -> &'a str {
        self.label
    }

    fn render(&self) -> Markup {
        (self.renderer)()
    }

    fn param_name(&self) -> &'a str {
        self.param_name
    }

    fn value(&self) -> Option<&'a str> {
        self.value
    }
}

pub fn detail_table<'a, T>(entities: impl IntoIterator<Item = T>) -> DetailTable<'a, T> {
    DetailTable {
        heading: "", // Default heading
        subheading: None,
        columns: Vec::new(),
        entities: entities.into_iter().collect(),
        filters: Vec::new(),
        form_action: None,
    }
}

impl<'a, T> DetailTable<'a, T> {
    // Adds a custom column with a provided label and HTML renderer
    pub fn add_column(&mut self, header: &'a str, renderer: impl Fn(&T) -> Markup + 'a) {
        self.columns.push(Column::new(header, renderer));
    }

    // Adds a "Name" column
    pub fn add_name_column<F>(&mut self, get_value: F)
    where
        F: for<'b> Fn(&'b T) -> &'b str + 'a,
    {
        self.add_column("Name", move |entity| html! { (get_value(entity)) });
    }

    // Adds an "Email" column
    pub fn add_email_column<F>(&mut self, get_value: F)
    where
        F: for<'b> Fn(&'b T) -> &'b str + 'a,
    {
        self.add_column("Email", move |entity| html! { (get_value(entity)) });
    }

    // Adds a "Created On" timestamp column
    pub fn add_create_ts<F>(&mut self, get_ts: F)
    where
        F: for<'b> Fn(&'b T) -> granite::DateTimeUtc + 'a,
    {
        self.add_column("Created On", move |entity| {
            let ts = get_ts(entity);
            html! {
                (ts.format("%b %d, %Y %I:%M %p"))
            }
        });
    }

    // Adds a "Status" column with colored labels for Active/Inactive
    pub fn add_status_column<F>(&mut self, header: &'a str, get_value: F)
    where
        F: for<'b> Fn(&'b T) -> &'b str + 'a,
    {
        self.add_column(header, move |entity| {
            let status = get_value(entity).to_lowercase();
            html! {
                @if status == "active" {
                    label-tag.primary { "Active" }
                } @else {
                    label-tag.default { "Inactive" }
                }
            }
        });
    }

    // Adds a "Status" column with colored labels for Active/Inactive
    pub fn add_active_status_column<F>(&mut self, header: &'a str, get_value: F)
    where
        F: for<'b> Fn(&'b T) -> bool + 'a,
    {
        self.add_column(header, move |entity| {
            let is_active = get_value(entity);
            html! {
                @if is_active {
                    label-tag.primary { "Active" }
                } @else if !is_active {
                    label-tag.default { "Inactive" }
                } @else {
                    label-tag.warning { "Invalid Status" }
                }
            }
        });
    }

    // Adds a "Debt Status" column with colored labels
    pub fn add_debt_status_column<F>(&mut self, header: &'a str, get_value: F)
    where
        F: for<'b> Fn(&'b T) -> &'b str + 'a,
    {
        self.add_column(header, move |entity| {
            let status = get_value(entity).to_lowercase();
            html! {
                @match status.as_str() {
                    "not started" => {
                        label-tag.default { "Not Started" }
                    },
                    "paying off" => {
                        label-tag.primary { "Paying Off" }
                    },
                    "almost done" => {
                        label-tag.warning { "Almost Done" }
                    },
                    "paid off" => {
                        label-tag.success { "Paid Off" }
                    },
                    _ => {
                        label-tag { "" }
                    }
                }
            }
        });
    }

    // Adds a "Name" column with additional metadata like "Added on <date>"
    pub fn add_name_meta<F, G>(
        &mut self,
        column_header: &'a str,
        label: &'a str,
        get_name: F,
        get_date: G,
    ) where
        F: for<'b> Fn(&'b T) -> &'b str + 'a,
        G: for<'b> Fn(&'b T) -> &'b str + 'a,
    {
        self.add_column(column_header, move |entity| {
            html! {
                div {
                    strong { (get_name(entity)) }
                    div.small {
                        (format!("{} {}", label, get_date(entity)))
                    }
                }
            }
        });
    }

    // Adds a "DF4L" (Debt2Capital™) column with different label colors based on status
    pub fn add_df4l_status<F>(&mut self, header: &'a str, get_status: F)
    where
        F: for<'b> Fn(&'b T) -> Df4lStatus + 'a,
    {
        self.add_column(header, move |entity| {
            let status = get_status(entity);
            html! {
                @match status {
                    Df4lStatus::Active(ref s) => label-tag.primary { (s) },
                    Df4lStatus::HasDataButNotEnrolled => label-tag.warning { "Has Data But Not Enrolled" },
                    Df4lStatus::NotEnrolled => label-tag.default { "Not Enrolled" },
                }
            }
        });
    }

    /// Adds a column with a clickable link using dynamic label and href.
    pub fn add_link_column<F1, F2>(
        &mut self,
        column_name: &'static str,
        get_href: F1,
        get_label: F2,
    ) where
        F1: Fn(&T) -> String + 'a,
        F2: Fn(&T) -> String + 'a,
    {
        self.add_column(column_name, move |entity| {
            let href = get_href(entity);
            let label = get_label(entity);
            html! {
                a href=(href) {
                    (label)
                }
            }
        });
    }

    // Adds two button columns with dynamic labels and hrefs
    // Usage: dt.add_two_button_column("th_name", "btn1_name", "btn1_icon_class", "btn1_color", |_| "your_url".to_string(), "btn2_name", "btn2_icon_class", "btn2_color", |_| "your_url".to_string());
    pub fn add_two_button_column<F1, S1, F2, S2>(
        &mut self,
        column_name: &'a str,
        btn1: ButtonConfig<'a>,
        get_url1: F1,
        btn2: ButtonConfig<'a>,
        get_url2: F2,
    ) where
        F1: for<'b> Fn(&'b T) -> S1 + 'a,
        F2: for<'b> Fn(&'b T) -> S2 + 'a,
        S1: Into<Cow<'a, str>>,
        S2: Into<Cow<'a, str>>,
    {
        self.add_column(column_name, move |entity| {
            let href1 = get_url1(entity).into();
            let href2 = get_url2(entity).into();
            html! {
                div style="display: flex; flex-direction: row; justify-content: flex-end; gap: 0.25rem;" {
                    a.(format!("btn {}", btn1.color)) href=(href1) {
                        i.(btn1.icon) aria-hidden="true" {} " " span { (btn1.name) }
                    }
                    a.(format!("btn {}", btn2.color)) href=(href2) {
                        i.(btn2.icon) aria-hidden="true" {} " " span { (btn2.name) }
                    }
                }
            }
        });
    }

    // Adds a button column with a dynamic label and href
    // Usage: dt.action_button_column("th_name", "btn_name", "icon_class", "btn_color", |_| "your_url".to_string());
    pub fn action_button_column<F, S>(
        &mut self,
        column_name: &'a str,
        btn_name: &'a str,
        icon_class: &'a str,
        btn_color: &'a str,
        get_url: F,
    ) where
        F: for<'b> Fn(&'b T) -> S + 'a,
        S: Into<Cow<'a, str>>,
    {
        self.add_column(column_name, move |entity| {
            let href = get_url(entity).into();
            let class = format!("btn {}", btn_color); // Precompute class
            html! {
                a.(class) href=(href) {
                    i.(icon_class) aria-hidden="true" {}
                    " "
                    span { (btn_name) }
                }
            }
        });
    }

    // Add a "Add" button column that links to a URL
    // Usage: dt.add_add_button_column("th_name", "btn_name", |_| "your_url".to_string());
    pub fn add_button_column<F, S>(&mut self, column_name: &'a str, btn_name: &'a str, get_url: F)
    where
        F: for<'b> Fn(&'b T) -> S + 'a,
        S: Into<Cow<'a, str>>,
    {
        self.add_column(column_name, move |entity| {
            let href = get_url(entity).into();
            html! {
                a.btn.primary href=(href) {
                    i.fas.fa-plus aria-hidden="true" {}
                    " "
                    span { (btn_name) }
                }
            }
        });
    }

    // Adds a "Delete" button column that links to a URL
    // Usage: dt.add_delete_button_column("th_name", "btn_name", |_| "your_url".to_string());
    pub fn add_delete_button_column<F, S>(
        &mut self,
        column_name: &'a str,
        btn_name: &'a str,
        get_url: F,
    ) where
        F: for<'b> Fn(&'b T) -> S + 'a,
        S: Into<Cow<'a, str>>,
    {
        self.add_column(column_name, move |entity| {
            let href = get_url(entity).into();
            html! {
                a.btn.warning href=(href) {
                    i.far.fa-trash-alt aria-hidden="true" {}
                    " "
                    span { (btn_name) }
                }
            }
        });
    }

    // Adds a "Edit" button column that links to a URL
    // Usage: dt.add_edit_button_column("th_name", "btn_name", |_| "your_url".to_string());
    pub fn add_edit_button_column<F, S>(
        &mut self,
        column_name: &'a str,
        btn_name: &'a str,
        get_url: F,
    ) where
        F: for<'b> Fn(&'b T) -> S + 'a,
        S: Into<Cow<'a, str>>,
    {
        self.add_column(column_name, move |entity| {
            let href = get_url(entity).into();
            html! {
                a.btn href=(href) {
                    i.fas.fa-pencil-alt aria-hidden="true" {}
                    " "
                    span { (btn_name) }
                }
            }
        });
    }

    // Adds a "Details" button column that links to a URL
    // Usage: dt.add_details_column(|_| "your_url");
    pub fn add_details_column<F, S>(&mut self, get_value: F)
    where
        F: for<'b> Fn(&'b T) -> S + 'a,
        S: Into<Cow<'a, str>>,
    {
        self.add_column("Details", move |entity| {
            let href = get_value(entity).into();
            html! {
                a.btn href=(href) {
                    i.fas.fa-chevron-right aria-hidden="true" {}
                    " "
                    span { "Details" }
                }
            }
        });
    }

    pub fn add_entity(&mut self, entity: T) {
        self.entities.push(entity);
    }

    pub fn set_heading(&mut self, heading: &'a str) {
        self.heading = heading;
    }

    pub fn set_subheading(&mut self, subheading: &'a str) {
        self.subheading = Some(subheading);
    }

    pub fn add_entities<I>(&mut self, entities: I)
    where
        I: IntoIterator<Item = T>,
    {
        self.entities.extend(entities);
    }

    pub fn add_keyword_filter(&mut self, value: Option<&'a str>) -> &mut Self {
        self.filters.push(Box::new(KeywordFilter::new(
            "Keyword Search",
            "keyword",
            value,
        )));
        self
    }

    pub fn add_active_filter(&mut self, value: Option<bool>) -> &mut Self {
        let value = match value {
            None => Some("all"),
            Some(true) => Some("active"),
            Some(false) => Some("inactive"),
        };

        self.filters.push(Box::new(SelectFilter::new(
            "Status",
            "active",
            vec![
                ("all", "Show Active and Inactive"),
                ("active", "Show Active"),
                ("inactive", "Show Inactive"),
            ],
            value,
        )));
        self
    }

    pub fn add_select_filter(
        &mut self,
        label: &'a str,
        param_name: &'a str,
        options: Vec<(&'a str, &'a str)>,
        value: Option<&'a str>,
    ) -> &mut Self {
        self.filters.push(Box::new(SelectFilter::new(
            label, param_name, options, value,
        )));
        self
    }

    pub fn add_custom_filter(
        &mut self,
        label: &'a str,
        param_name: &'a str,
        value: Option<&'a str>,
        renderer: impl Fn() -> Markup + 'a,
    ) -> &mut Self {
        self.filters.push(Box::new(CustomFilter::new(
            label, param_name, value, renderer,
        )));
        self
    }

    pub fn add_filter(&mut self, filter: impl Filter<'a> + 'a) -> &mut Self {
        self.filters.push(Box::new(filter));
        self
    }

    pub fn add_extra_payof_value<F, G>(
        &mut self,
        header: &'a str,
        get_extra_value: F,
        get_payoff_value: G,
    ) where
        F: for<'b> Fn(&'b T) -> &'b str + 'a,
        G: for<'b> Fn(&'b T) -> &'b str + 'a,
    {
        self.add_column(header, move |entity| {
            html! {
                div {
                    div {
                        span { (get_extra_value(entity)) }
                    }
                    div {
                        span { (get_payoff_value(entity)) }
                    }
                }
            }
        });
    }
}

impl<T> Render for DetailTable<'_, T> {
    fn render(&self) -> Markup {
        html! {
            bux-component-detail-table {
                h2 { (self.heading) }
                @if let Some(subheading) = self.subheading {
                    p { (subheading) }
                }

                @if !self.filters.is_empty() {
                    panel.filter-panel {
                        content {
                            form method="GET" action=(self.form_action.unwrap_or("")) {
                                grid-3 {
                                    @for filter in &self.filters {
                                        (filter.render())
                                    }

                                    div.filter-actions {
                                        button.primary type="submit" {
                                            i.fas.fa-filter {} " Filter"
                                        }
                                        " "
                                        @if !self.filters.is_empty() {
                                            a.btn.secondary href="?" {
                                                i.fas.fa-undo {} " Reset"
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                table-wrapper.data-list {
                    table {
                        @if !self.columns.is_empty() {
                            thead {
                                tr {
                                    @for column in &self.columns {
                                        th { (column.header) }
                                    }
                                }
                            }
                        }
                        tbody {
                            @for entity in &self.entities {
                                tr {
                                    @for column in &self.columns {
                                        td { (column.render_cell(entity)) }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

// For backward compatibility
pub struct Entity<'a> {
    pub name: &'a str,
    pub href: &'a str,
}

// Legacy implementation for backward compatibility
impl<'a> DetailTable<'a, Entity<'a>> {
    pub fn append(&mut self, name: &'a str, href: &'a str) {
        self.entities.push(Entity { name, href });
    }
}
