use crate::Decimal;
use crate::dec;
use rust_decimal::prelude::ToPrimitive;
use std::collections::{HashMap, HashSet};
use std::net::IpAddr;

/// GType serialize trait
/// Put more documentation here
/// You can use `formatting` and **other markdown** here to make it nice
/// ```text
/// println!("Hello, world!");
/// ```
pub trait GTypeEncode {
    fn gtype_encode(&self) -> crate::JsonValue;
    fn gtype_encode_string(&self) -> crate::Result<String> {
        Ok(serde_json::to_string(&self.gtype_encode())?)
    }
    fn gtype_encode_string_indented(&self) -> crate::Result<String> {
        Ok(serde_json::to_string_pretty(&self.gtype_encode())?)
    }
}

/// GType deserialize trait for when the error type is always a string
pub trait GTypeDecode {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized;
}

pub trait GTypeValidate {
    type Type;
    type Error;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error>;
}

//------------------------------------------------------------------------------------------------

// Used for errors that can recurse (Vec<T>, HashMap<K,V>, etc...)
#[allow(non_camel_case_types)]
#[derive(Debug)]
pub struct NestedError<E> {
    pub outer: String,
    pub inner: Option<E>,
}

impl<E> NestedError<E> {
    pub fn message(message: &str) -> Self {
        Self {
            outer: message.to_string(),
            inner: None,
        }
    }

    pub fn new_string(message: String) -> Self {
        Self {
            outer: message,
            inner: None,
        }
    }

    pub fn value_is_none() -> Self {
        Self::message("value is none")
    }

    pub fn wrong_json_type(json_value: serde_json::Value) -> Self {
        let incoming_type = match json_value {
            serde_json::Value::Null => "null",
            serde_json::Value::Bool(_) => "bool",
            serde_json::Value::Number(_) => "number",
            serde_json::Value::String(_) => "string",
            serde_json::Value::Array(_) => "array",
            serde_json::Value::Object(_) => "object",
        };

        Self::new_string(format!("wrong json type: {}", incoming_type))
    }
}

impl<E> GTypeEncode for NestedError<E>
where
    E: GTypeEncode,
{
    fn gtype_encode(&self) -> crate::JsonValue {
        let mut map = serde_json::Map::new();
        map.insert(
            "Outer".to_string(),
            serde_json::Value::String(self.outer.clone()),
        );
        if let Some(inner) = &self.inner {
            map.insert("Inner".to_string(), inner.gtype_encode());
        }
        serde_json::Value::Object(map)
    }
}

impl<T> GTypeDecode for NestedError<T>
where
    T: GTypeDecode,
{
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        // First attempt to decode as a complete NestedError
        match data {
            Some(serde_json::Value::Object(mut map)) => {
                let outer = match map.remove("Outer") {
                    Some(serde_json::Value::String(s)) => s,
                    Some(v) => return Err(format!("NestedError Outer decode failed: {:?}", v)),
                    None => return Err("NestedError Outer missing".to_string()),
                };

                let inner = match map.remove("Inner") {
                    Some(v) => Some(T::gtype_decode(Some(v))?),
                    None => None,
                };

                Ok(Self { outer, inner })
            }
            Some(v) => Err(format!("NestedError: wrong json type: {:?}", v)),
            None => Err("NestedError: value is undefined".to_string()),
        }
    }
}

impl GTypeEncode for NestedError<()> {
    fn gtype_encode(&self) -> crate::JsonValue {
        let mut map = serde_json::Map::new();
        map.insert(
            "Outer".to_string(),
            serde_json::Value::String(self.outer.clone()),
        );
        serde_json::Value::Object(map)
    }
}

impl GTypeDecode for NestedError<()> {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::Object(mut map)) => {
                let outer = match map.remove("Outer") {
                    Some(serde_json::Value::String(s)) => s,
                    Some(v) => return Err(format!("NestedError Outer decode failed: {:?}", v)),
                    None => return Err("NestedError Outer missing".to_string()),
                };

                Ok(Self { outer, inner: None })
            }
            Some(v) => Err(format!("NestedError: wrong json type: {:?}", v)),
            None => Err("NestedError: value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for char {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// char is encoded as a hex string in JSON
impl GTypeEncode for char {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::String(format!("{:X}", *self as u32))
    }
}

/// char is encoded as a hex string in JSON
impl GTypeDecode for char {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::String(s)) => match u32::from_str_radix(&s, 16) {
                Ok(v) => match std::char::from_u32(v) {
                    Some(c) => Ok(c),
                    None => Err("char: convert to char error".to_string()),
                },
                Err(_) => Err("char: decode from hex string error".to_string()),
            },
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for bool {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// bool is encoded as true|false in JSON
impl GTypeEncode for bool {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::Bool(*self)
    }
}

/// bool is encoded as true|false in JSON
impl GTypeDecode for bool {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::Bool(b)) => Ok(b),
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for i8 {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// i8 is encoded as a string in JSON
impl GTypeEncode for i8 {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::String(self.to_string())
    }
}

/// i8 is encoded as a string in JSON
impl GTypeDecode for i8 {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::String(s)) => match s.parse() {
                Ok(v) => Ok(v),
                Err(e) => Err(format!("i8 error: {e}")),
            },
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for u8 {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// u8 is encoded as a string in JSON
impl GTypeEncode for u8 {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::String(self.to_string())
    }
}

/// u8 is encoded as a string in JSON
impl GTypeDecode for u8 {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::String(s)) => match s.parse() {
                Ok(v) => Ok(v),
                Err(e) => Err(format!("u8 error: {e}")),
            },
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for i16 {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// i16 is encoded as a string in JSON
impl GTypeEncode for i16 {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::String(self.to_string())
    }
}

/// i16 is encoded as a string in JSON
impl GTypeDecode for i16 {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::String(s)) => match s.parse() {
                Ok(v) => Ok(v),
                Err(e) => Err(format!("i16 error: {e}")),
            },
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for u16 {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// u16 is encoded as a string in JSON
impl GTypeEncode for u16 {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::String(self.to_string())
    }
}

/// u16 is encoded as a string in JSON
impl GTypeDecode for u16 {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::String(s)) => match s.parse() {
                Ok(v) => Ok(v),
                Err(e) => Err(format!("u16 error: {e}")),
            },
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for i32 {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// i32 is encoded as a string in JSON
impl GTypeEncode for i32 {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::String(self.to_string())
    }
}

/// i32 is encoded as a string in JSON
impl GTypeDecode for i32 {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::String(s)) => match s.parse() {
                Ok(v) => Ok(v),
                Err(e) => Err(format!("i32 error: {e}")),
            },
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for u32 {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// u32 is encoded as a string in JSON
impl GTypeEncode for u32 {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::String(self.to_string())
    }
}

/// u32 is encoded as a string in JSON
impl GTypeDecode for u32 {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::String(s)) => match s.parse() {
                Ok(v) => Ok(v),
                Err(e) => Err(format!("u32 error: {e}")),
            },
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for i64 {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// i64 is encoded as a string in JSON
impl GTypeEncode for i64 {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::String(self.to_string())
    }
}

/// i64 is encoded as a string in JSON
impl GTypeDecode for i64 {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::String(s)) => match s.parse() {
                Ok(v) => Ok(v),
                Err(e) => Err(format!("i64 error: {e}")),
            },
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for u64 {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// u64 is encoded as a string in JSON
impl GTypeEncode for u64 {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::String(self.to_string())
    }
}

/// u64 is encoded as a string in JSON
impl GTypeDecode for u64 {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::String(s)) => match s.parse() {
                Ok(v) => Ok(v),
                Err(e) => Err(format!("u64 error: {e}")),
            },
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for usize {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// usize is encoded as a number in JSON
impl GTypeEncode for usize {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::Number(serde_json::Number::from(*self))
    }
}

/// usize is decoded from a JSON number
impl GTypeDecode for usize {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::Number(n)) => n
                .as_u64()
                .ok_or_else(|| "invalid integer value".to_string())?
                .try_into()
                .map_err(|_| "number too large for usize".to_string()),
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for f32 {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// f32 is encoded as a string in JSON
impl GTypeEncode for f32 {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::String(self.to_string())
    }
}

/// f32 is encoded as a string in JSON
impl GTypeDecode for f32 {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::String(s)) => match s.parse() {
                Ok(v) => Ok(v),
                Err(e) => Err(format!("f32 error: {e}")),
            },
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for f64 {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// f64 is encoded as a string in JSON
impl GTypeEncode for f64 {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::String(self.to_string())
    }
}

/// f64 is encoded as a string in JSON
impl GTypeDecode for f64 {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::String(s)) => match s.parse() {
                Ok(v) => Ok(v),
                Err(e) => Err(format!("f64 error: {e}")),
            },
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for String {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// String is encoded as a string in JSON
impl GTypeEncode for String {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::String(self.clone())
    }
}

/// String is encoded as a string in JSON
impl GTypeDecode for String {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::String(s)) => Ok(s),
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for crate::Uuid {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// Uuid is encoded as a string in JSON
impl GTypeEncode for crate::Uuid {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::String(self.to_string())
    }
}

/// String is encoded as a string in JSON
impl GTypeDecode for crate::Uuid {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::String(s)) => match crate::Uuid::parse_str(&s) {
                Ok(v) => Ok(v),
                Err(e) => Err(format!("Uuid error: {e}")),
            },
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for crate::Decimal {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// Decimal is encoded as a string in JSON
impl GTypeEncode for crate::Decimal {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::String(self.to_string())
    }
}

/// Decimal is encoded as a string in JSON
impl GTypeDecode for crate::Decimal {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::String(s)) => match std::str::FromStr::from_str(&s) {
                Ok(v) => Ok(v),
                Err(e) => Err(format!("Decimal error: {e}")),
            },
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for crate::BigInt {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// BigInt is encoded as a string in JSON
impl GTypeEncode for crate::BigInt {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::String(self.to_string())
    }
}

/// BigInt is encoded as a string in JSON
impl GTypeDecode for crate::BigInt {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::String(s)) => match std::str::FromStr::from_str(&s) {
                Ok(v) => Ok(v),
                Err(e) => Err(format!("BigInt error: {e}")),
            },
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for chrono::NaiveDate {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// DateUtc is encoded as "yyyy-mm-dd"
impl GTypeEncode for chrono::NaiveDate {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::String(self.to_string())
    }
}

/// DateUtc is encoded as "yyyy-mm-dd"
impl GTypeDecode for chrono::NaiveDate {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::String(s)) => {
                match chrono::NaiveDate::parse_from_str(&s, "%Y-%m-%d") {
                    Ok(v) => Ok(v),
                    Err(e) => Err(format!("DateUtc error: {e}")),
                }
            }
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for chrono::NaiveDateTime {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// Naive DateTime is encoded as "yyyy-mm-ddThh:mm:ss"
impl GTypeEncode for chrono::NaiveDateTime {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::String(self.to_string())
    }
}

/// Naive DateTime is encoded as "yyyy-mm-ddThh:mm:ss"
impl GTypeDecode for chrono::NaiveDateTime {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::String(s)) => {
                match chrono::NaiveDateTime::parse_from_str(&s, "%Y-%m-%d %H:%M:%S") {
                    Ok(v) => Ok(v),
                    Err(e) => Err(format!(
                        "NaiveDateTime: convert to NaiveDateTime error: {e}"
                    )),
                }
            }
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for crate::DateTimeUtc {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

impl GTypeEncode for crate::DateTimeUtc {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::String(self.to_rfc3339())
    }
}

impl GTypeDecode for crate::DateTimeUtc {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::String(s)) => match chrono::DateTime::parse_from_rfc3339(&s) {
                Ok(v) => Ok(v.with_timezone(&chrono::Utc)),
                Err(e) => Err(format!("DateTimeUtc: convert to DateTimeUtc error: {e}")),
            },
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for crate::DateTimeTz {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// DateTimeTz is encoded as yyyy-mm-ddThh:mm:ss+HH:MM
impl GTypeEncode for crate::DateTimeTz {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::String(self.to_rfc3339())
    }
}

/// DateTimeTz is encoded as yyyy-mm-ddThh:mm:ss+HH:MM
impl GTypeDecode for crate::DateTimeTz {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::String(s)) => match chrono::DateTime::parse_from_rfc3339(&s) {
                Ok(v) => Ok(v),
                Err(e) => Err(format!("DateTimeTz: convert to DateTimeTz error: {e}")),
            },
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for chrono::NaiveTime {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// Time is encoded as "hh:mm:ss"
impl GTypeEncode for chrono::NaiveTime {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::String(self.to_string())
    }
}

/// Time is encoded as "hh:mm:ss"
impl GTypeDecode for chrono::NaiveTime {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::String(s)) => chrono::NaiveTime::parse_from_str(&s, "%H:%M:%S")
                .map_err(|e| format!("Time: convert to Time error: {e}")),
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for chrono::Duration {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// Duration is encoded as "seconds.fraction" in JSON
impl GTypeEncode for chrono::Duration {
    fn gtype_encode(&self) -> serde_json::Value {
        // Convert the fractional seconds to a decimal
        let nanoseconds = Decimal::from(self.subsec_nanos());
        let seconds = Decimal::from(self.num_seconds());

        let total_seconds = format!("PT{}S", seconds + (nanoseconds * dec!(0.000000001)));

        // Return the JSON object
        serde_json::Value::String(total_seconds)
    }
}

// Duration is encoded as "seconds.fraction" in JSON
impl GTypeDecode for chrono::Duration {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        // Parse the JSON value
        match data {
            Some(serde_json::Value::String(s)) => {
                // Check the string format
                if !s.starts_with("PT") || !s.ends_with('S') {
                    return Err("Duration: invalid format".to_string());
                }

                // Convert the string to a Decimal
                let s = s.trim_start_matches("PT").trim_end_matches('S');
                let decimal = match Decimal::from_str_exact(s) {
                    Ok(v) => v,
                    Err(_) => {
                        return Err("Duration: convert to Decimal error".to_string());
                    }
                };

                // Get the seconds and fractional seconds
                let seconds = decimal
                    .to_i64()
                    .ok_or_else(|| "Duration: convert to Decimal error".to_string())?;

                // Convert fractional part to nanoseconds
                let fractional_seconds = decimal.fract();
                let nanoseconds = (fractional_seconds * Decimal::from(1_000_000_000))
                    .to_u32()
                    .ok_or_else(|| "Duration: convert to Decimal error".to_string())?;

                //let nanoseconds = (fractional_seconds * dec!(1000000000)).to_u32().ok_or_else(|| GErr::new("Duration: convert to Decimal error"))?;

                // Create the chrono::Duration
                let time = chrono::Duration::new(seconds, nanoseconds);

                // Return the duration
                Ok(time.unwrap())
            }
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl<T> GTypeValidate for Option<T>
where
    T: GTypeValidate<Type = T>,
{
    type Type = Self;
    type Error = T::Error;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        match self {
            Some(v) => Ok(Some(v.gtype_validate()?)),
            None => Ok(None),
        }
    }
}

/// Option is encoded as {"Some": T} | {"None": True}
impl<T> GTypeEncode for Option<T>
where
    T: GTypeEncode,
{
    fn gtype_encode(&self) -> serde_json::Value {
        let mut map = serde_json::Map::new();
        match self {
            Some(v) => {
                map.insert("Some".to_string(), v.gtype_encode());
            }
            None => {
                map.insert("None".to_string(), serde_json::Value::Bool(true));
            }
        }
        serde_json::Value::Object(map)
    }
}

/// Option is encoded as {"Some": T} | {"None": True}
impl<T> GTypeDecode for Option<T>
where
    T: GTypeDecode,
{
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::Object(mut map)) => {
                if let Some(json_value) = map.remove("Some") {
                    match T::gtype_decode(Some(json_value)) {
                        Ok(value) => Ok(Some(value)),
                        Err(e) => Err(e),
                    }
                } else if map.remove("None").is_some() {
                    Ok(None)
                } else {
                    Err("Option: json object missing key (Some|None)".to_string())
                }
            }
            Some(serde_json::Value::Null) => Ok(None),
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

/// Result<T,E> is encoded as {Ok: T} | {Err: E} in JSON
impl<T, E> GTypeEncode for Result<T, E>
where
    T: GTypeEncode,
    E: GTypeEncode,
{
    fn gtype_encode(&self) -> serde_json::Value {
        match self {
            Ok(v) => {
                let mut map = serde_json::Map::new();
                map.insert("Ok".to_string(), v.gtype_encode());
                serde_json::Value::Object(map)
            }
            Err(e) => {
                let mut map = serde_json::Map::new();
                map.insert("Err".to_string(), e.gtype_encode());
                serde_json::Value::Object(map)
            }
        }
    }
}

/// Result<T,E> is encoded as {Ok: T} | {Err: E} in JSON
/// Valid Returns:
///     Ok(Ok(OK_TYPE))
///     Ok(Err(ERR_TYPE))
///     Err(String)
///
/// This is restructed to <ErrorType = String> for the nested types to avoid the unnecessary complexity
/// of nesting an error type which is used to convery errors anyway (e.g. Result is not for user input)
#[allow(non_camel_case_types)]
impl<OK_TYPE, ERR_TYPE> GTypeDecode for Result<OK_TYPE, ERR_TYPE>
where
    OK_TYPE: GTypeDecode,
    ERR_TYPE: GTypeDecode,
{
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Result<OK_TYPE, ERR_TYPE>, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::Object(mut map)) => {
                if let Some(ok) = map.remove("Ok") {
                    // we have an ok value, we need to filter it through it's cooresponding type
                    match OK_TYPE::gtype_decode(Some(ok)) {
                        Ok(ok) => Ok(Ok(ok)),
                        Err(e) => Err(format!("Error decoding Ok: {e}")),
                    }
                } else if let Some(err) = map.remove("Err") {
                    match ERR_TYPE::gtype_decode(Some(err)) {
                        Ok(err) => Ok(Err(err)),
                        Err(e) => Err(format!("Error decoding Err: {e}")),
                    }
                } else {
                    Err("Result: json object missing key (Ok|Err)".to_string())
                }
            }
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl<T> GTypeValidate for Vec<T>
where
    T: GTypeValidate<Type = T>,
{
    type Type = Self;
    type Error = NestedError<Vec<Option<T::Error>>>;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        let mut rval = Vec::with_capacity(self.len());
        let mut errs = false;

        for v in self.into_iter() {
            let v = v.gtype_validate();
            if v.is_err() {
                errs = true;
            }
            rval.push(v);
        }

        if errs {
            let rval = rval.into_iter().map(|v| v.err()).collect::<Vec<_>>();
            Err(NestedError {
                outer: "validation error".to_string(),
                inner: Some(rval),
            })
        } else {
            let rval = rval
                .into_iter()
                .map(|v| v.unwrap_or_else(|_| unreachable!()))
                .collect::<Vec<_>>();
            Ok(rval)
        }
    }
}

//-------------------------------------------------------------------------------------------------

/// Vec<T> is encoded as [T, T, ...] in JSON
impl<T> GTypeEncode for Vec<T>
where
    T: GTypeEncode,
{
    fn gtype_encode(&self) -> serde_json::Value {
        let mut arr = Vec::new();
        for v in self {
            arr.push(v.gtype_encode());
        }
        serde_json::Value::Array(arr)
    }
}

impl<T> GTypeDecode for Vec<T>
where
    T: GTypeDecode,
{
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::Array(arr)) => {
                let mut vec_t = Vec::with_capacity(arr.len());
                let mut errors = Vec::new();

                for (i, v) in arr.into_iter().enumerate() {
                    match T::gtype_decode(Some(v)) {
                        Ok(t) => vec_t.push(t),
                        Err(e) => errors.push(format!("Element {}: {}", i, e)),
                    }
                }

                if errors.is_empty() {
                    Ok(vec_t)
                } else {
                    Err(errors.join("; "))
                }
            }
            Some(v) => Err(format!("Vec: wrong json type: {:?}", v)),
            None => Err("Vec: value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl<T> GTypeValidate for HashSet<T>
where
    T: GTypeValidate<Type = T> + Eq + ::std::hash::Hash,
{
    type Type = Self;
    type Error = NestedError<Vec<T::Error>>;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        let mut rval = HashSet::with_capacity(self.len());
        let mut errs = Vec::new();

        for v in self.into_iter() {
            match v.gtype_validate() {
                Ok(v) => {
                    rval.insert(v);
                }
                Err(e) => {
                    errs.push(e);
                }
            }
        }

        if errs.is_empty() {
            Ok(rval)
        } else {
            Err(NestedError {
                outer: "validation error".to_string(),
                inner: Some(errs),
            })
        }
    }
}

/// HashSet<T> is encoded as [T, T, ...] in JSON
impl<T> GTypeEncode for HashSet<T>
where
    T: GTypeEncode,
{
    fn gtype_encode(&self) -> serde_json::Value {
        let mut arr = Vec::new();
        for v in self {
            arr.push(v.gtype_encode());
        }
        serde_json::Value::Array(arr)
    }
}

impl<T> GTypeDecode for HashSet<T>
where
    T: GTypeDecode + std::hash::Hash + std::cmp::Eq,
{
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::Array(arr)) => {
                let mut hashset_t = HashSet::with_capacity(arr.len());
                let mut errors = Vec::new();

                for (i, v) in arr.into_iter().enumerate() {
                    match T::gtype_decode(Some(v)) {
                        Ok(t) => {
                            hashset_t.insert(t);
                        }
                        Err(e) => errors.push(format!("Element {}: {}", i, e)),
                    }
                }

                if errors.is_empty() {
                    Ok(hashset_t)
                } else {
                    Err(errors.join("; "))
                }
            }
            Some(v) => Err(format!("HashSet: wrong json type: {:?}", v)),
            None => Err("HashSet: value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl<K, V> GTypeValidate for HashMap<K, V>
where
    K: GTypeValidate<Type = K> + std::cmp::Eq + std::hash::Hash,
    V: GTypeValidate<Type = V>,
{
    type Type = Self;
    type Error = NestedError<HashMap<K, V::Error>>;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        let mut rval = HashMap::with_capacity(self.len());
        let mut errs = HashMap::new();

        for (k, v) in self.into_iter() {
            match v.gtype_validate() {
                Ok(v) => {
                    rval.insert(k, v);
                }
                Err(e) => {
                    errs.insert(k, e);
                }
            }
        }

        if errs.is_empty() {
            Ok(rval)
        } else {
            Err(NestedError {
                outer: "validation error".to_string(),
                inner: Some(errs),
            })
        }
    }
}

/// HashMap<K,V> is encoded as {K: V, K: V, ...} in JSON
impl<K, V> GTypeEncode for std::collections::HashMap<K, V>
where
    K: GTypeEncode + std::cmp::Eq + std::hash::Hash,
    V: GTypeEncode,
{
    fn gtype_encode(&self) -> serde_json::Value {
        let mut rval = Vec::new();
        for (k, v) in self {
            let tuple = serde_json::Value::Array(vec![k.gtype_encode(), v.gtype_encode()]);
            rval.push(tuple);
        }
        serde_json::Value::Array(rval)
    }
}

impl<K, V> GTypeDecode for HashMap<K, V>
where
    K: GTypeDecode + std::fmt::Display + std::cmp::Eq + std::hash::Hash,
    V: GTypeDecode,
{
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        // Handle both array of tuples and object formats
        let items = match data {
            Some(serde_json::Value::Array(array)) => {
                let mut items = Vec::with_capacity(array.len());
                for (i, v) in array.into_iter().enumerate() {
                    match v {
                        serde_json::Value::Array(mut tuple) if tuple.len() == 2 => {
                            items.push((i, tuple.remove(0), tuple.remove(1)));
                        }
                        _ => {
                            return Err(format!(
                                "HashMap: element [{}] must be a tuple with length 2",
                                i
                            ));
                        }
                    }
                }
                items
            }
            Some(serde_json::Value::Object(map)) => map
                .into_iter()
                .enumerate()
                .map(|(i, (k, v))| (i, serde_json::Value::String(k), v))
                .collect(),
            Some(v) => return Err(format!("HashMap: wrong json type: {:?}", v)),
            None => return Err("HashMap: value is undefined".to_string()),
        };

        let mut hashmap = HashMap::with_capacity(items.len());
        let mut errors = Vec::new();

        for (i, key_json, value_json) in items {
            let key_result = K::gtype_decode(Some(key_json));
            let value_result = V::gtype_decode(Some(value_json));

            match (key_result, value_result) {
                (Ok(key), Ok(value)) => {
                    hashmap.insert(key, value);
                }
                (Err(key_err), _) => {
                    errors.push(format!("Key at position {}: {}", i, key_err));
                }
                (Ok(key), Err(value_err)) => {
                    errors.push(format!("Value for key '{}': {}", key, value_err));
                }
            }
        }

        if errors.is_empty() {
            Ok(hashmap)
        } else {
            Err(errors.join("; "))
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for serde_json::Value {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// JsonValue is encoded as itself in JSON
impl GTypeEncode for serde_json::Value {
    fn gtype_encode(&self) -> serde_json::Value {
        self.clone()
    }
}

/// JsonValue is encoded as itself in JSON
impl GTypeDecode for serde_json::Value {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(v) => Ok(v),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for crate::JsonObject {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// JsonObject is encoded as a JSON object
impl GTypeEncode for crate::JsonObject {
    fn gtype_encode(&self) -> crate::JsonValue {
        crate::JsonValue::Object(self.clone())
    }
}

/// JsonObject is encoded as a JSON object
impl GTypeDecode for crate::JsonObject {
    fn gtype_decode(data: Option<crate::JsonValue>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(crate::JsonValue::Object(map)) => Ok(map),
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

//-------------------------------------------------------------------------------------------------

impl GTypeValidate for IpAddr {
    type Type = Self;
    type Error = String;
    fn gtype_validate(self) -> Result<Self::Type, Self::Error> {
        Ok(self)
    }
}

/// IpAddr is encoded as a string in JSON
impl GTypeEncode for IpAddr {
    fn gtype_encode(&self) -> serde_json::Value {
        serde_json::Value::String(self.to_string())
    }
}

/// IpAddr is encoded as a string in JSON
impl GTypeDecode for IpAddr {
    fn gtype_decode(data: Option<serde_json::Value>) -> Result<Self, String>
    where
        Self: Sized,
    {
        match data {
            Some(serde_json::Value::String(s)) => match s.parse() {
                Ok(v) => Ok(v),
                Err(e) => Err(format!("IpAddr error: {e}")),
            },
            Some(v) => Err(format!("wrong json type: {v:?}")),
            None => Err("value is undefined".to_string()),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::str::FromStr;

    #[test]
    fn test_ip_addr_encode_decode() {
        let ip_v4 = IpAddr::from_str("***********").unwrap();
        let encoded_v4 = ip_v4.gtype_encode();
        assert_eq!(
            encoded_v4,
            serde_json::Value::String("***********".to_string())
        );
        let decoded_v4 = IpAddr::gtype_decode(Some(encoded_v4)).unwrap();
        assert_eq!(decoded_v4, ip_v4);

        let ip_v6 = IpAddr::from_str("::1").unwrap();
        let encoded_v6 = ip_v6.gtype_encode();
        assert_eq!(encoded_v6, serde_json::Value::String("::1".to_string()));
        let decoded_v6 = IpAddr::gtype_decode(Some(encoded_v6)).unwrap();
        assert_eq!(decoded_v6, ip_v6);

        let invalid_ip_str = serde_json::Value::String("invalid-ip".to_string());
        let decoded_invalid = IpAddr::gtype_decode(Some(invalid_ip_str));
        assert!(decoded_invalid.is_err());

        let none_value = IpAddr::gtype_decode(None);
        assert!(none_value.is_err());

        let wrong_type = serde_json::Value::Bool(true);
        let decoded_wrong_type = IpAddr::gtype_decode(Some(wrong_type));
        assert!(decoded_wrong_type.is_err());
    }
}
