#[cfg(test)]
mod tests;

pub use redis::AsyncCommands;
use redis::{FromRedisValue, ToRedisArgs};
use tracing::{debug, info, instrument, trace};

#[derive(Debug, serde::Deserialize)]
pub struct ModuleConfig {
    pub host: String,
    pub port: Option<u16>,
    pub database: u16,
    pub connect_timeout: Option<u64>,
}

impl ModuleConfig {
    pub fn to_url(&self) -> String {
        format!(
            "redis://{}:{}/{}",
            self.host,
            self.port.unwrap_or(6379),
            self.database
        )
    }
}

#[allow(dead_code)]
pub struct ModuleStruct {
    config: ModuleConfig,
    pool: tokio::sync::OnceCell<RedisPool>,
}

impl approck::Module for ModuleStruct {
    type Config = ModuleConfig;

    fn new(config: Self::Config) -> granite::Result<Self> {
        Ok(Self {
            config,
            pool: tokio::sync::OnceCell::new(),
        })
    }

    async fn init(&self) -> granite::Result<()> {
        info!("Initializing Redis module");
        self.pool
            .get_or_try_init(|| RedisPool::new(&self.config))
            .await?;
        info!("Redis module initialized");
        Ok(())
    }
}

impl ModuleStruct {
    /// Get a cheap clone of the RedisPool
    pub async fn pool(&self) -> granite::Result<RedisPool> {
        let pool = self.pool.get().ok_or_else(|| {
            granite::Error::new(granite::ErrorType::InvalidOperation).add_context(
                "Redis module has not been initialized. Did you forget to call init()?",
            )
        })?;
        Ok(pool.clone())
    }

    /// Get a [`RedisCX`] directly. Most preferable way is to get a local [`RedisPool`] and call `.get()` to avoid lifetime issues.
    pub async fn get_dbcx(&self) -> granite::Result<RedisCX> {
        debug!("Getting Redis connection from module");
        let pool = self.pool.get().ok_or_else(|| {
            granite::Error::new(granite::ErrorType::InvalidOperation).add_context(
                "Redis module has not been initialized. Did you forget to call init()?",
            )
        })?;
        pool.get().await
    }
}

pub trait App {
    fn redis_dbcx(
        &self,
    ) -> impl std::future::Future<
        // LUKE: is this different than RedisCX?
        Output = granite::Result<RedisCX>,
    > + Send;
}

#[derive(Debug, Clone)]
pub struct RedisPool {
    pool: bb8::Pool<bb8_redis::RedisConnectionManager>,
}

impl RedisPool {
    pub async fn new(config: &ModuleConfig) -> granite::Result<Self> {
        let url = config.to_url();
        info!(url = %url, "Creating new Redis connection pool");

        let manager = bb8_redis::RedisConnectionManager::new(url)?;

        let pool = bb8::Pool::builder()
            .connection_timeout(std::time::Duration::from_secs(
                config.connect_timeout.unwrap_or(5),
            ))
            .build(manager)
            .await?;

        info!("Redis connection pool created");
        Ok(Self { pool })
    }

    pub async fn get(&self) -> granite::Result<RedisCX> {
        debug!("Requesting connection from Redis pool");
        let cx = self.pool.get().await?;
        debug!("Acquired connection from Redis pool");
        Ok(RedisCX { cx })
    }
}

#[derive(Debug)]
pub struct RedisCX<'a> {
    cx: bb8::PooledConnection<'a, bb8_redis::RedisConnectionManager>,
}

impl RedisCX<'_> {
    #[instrument(skip(self), fields(key = %key))]
    pub async fn get_val<T>(&mut self, key: &str) -> granite::Result<T>
    where
        T: redis::FromRedisValue,
    {
        trace!("GET");
        let value: T = self.cx.get(key).await?;
        Ok(value)
    }

    #[instrument(skip(self, keys))]
    pub async fn mget_val<T>(&mut self, keys: &[&str]) -> granite::Result<Vec<T>>
    where
        T: redis::FromRedisValue,
    {
        trace!(keys = ?keys, "MGET");
        let values = self.cx.mget(keys).await?;
        Ok(values)
    }

    #[instrument(skip(self, value), fields(key = %key))]
    pub async fn set_val<T>(&mut self, key: &str, value: T) -> granite::Result<()>
    where
        T: redis::ToRedisArgs + Send + Sync,
    {
        trace!("SET");
        self.cx.set::<_, _, ()>(key, value).await?;
        Ok(())
    }

    #[instrument(skip(self), fields(key = %key))]
    pub async fn exists(&mut self, key: &str) -> granite::Result<bool> {
        trace!("EXISTS");
        let exists = self.cx.exists(key).await?;
        Ok(exists)
    }

    #[instrument(skip(self), fields(key = %key))]
    pub async fn del(&mut self, key: &str) -> granite::Result<()> {
        trace!("DEL");
        self.cx.del::<_, ()>(key).await?;
        Ok(())
    }

    #[instrument(skip(self), fields(pattern = %pattern))]
    pub async fn keys_str(&mut self, pattern: &str) -> granite::Result<Vec<String>> {
        trace!("KEYS");
        let keys: Vec<String> = self.cx.keys(pattern).await?;
        Ok(keys)
    }

    #[instrument(skip(self, value), fields(key = %key))]
    pub async fn set_json<T>(&mut self, key: &str, value: &T) -> granite::Result<()>
    where
        T: serde::Serialize,
    {
        trace!("SET JSON");
        let value = serde_json::to_string(value)?;
        self.cx.set::<_, _, ()>(key, value).await?;
        Ok(())
    }

    #[instrument(skip(self), fields(key = %key))]
    pub async fn get_json<T>(&mut self, key: &str) -> granite::Result<T>
    where
        T: serde::de::DeserializeOwned,
    {
        trace!("GET JSON");
        let value: String = match self.cx.get(key).await {
            Ok(value) => value,
            Err(e) => {
                return Err(
                    granite::process_error!("Error getting value from redis").add_context(e)
                );
            }
        };
        match serde_json::from_str(&value) {
            Ok(value) => Ok(value),
            Err(e) => {
                Err(granite::process_error!("Error deserializing value from redis").add_context(e))
            }
        }
    }

    #[instrument(skip(self, keys))]
    pub async fn mget_json<T>(&mut self, keys: &[&str]) -> granite::Result<Vec<T>>
    where
        T: serde::de::DeserializeOwned,
    {
        trace!(keys = ?keys, "MGET JSON");
        let values: Vec<String> = self.cx.mget(keys).await?;
        let values: Vec<T> = values
            .into_iter()
            .map(|value| serde_json::from_str(&value))
            .collect::<Result<Vec<T>, _>>()?;
        Ok(values)
    }

    #[instrument(skip(self, value), fields(key = %key))]
    pub async fn append_str(&mut self, key: &str, value: &str) -> granite::Result<()> {
        trace!("APPEND");
        self.cx.append::<_, _, ()>(key, value).await?;
        Ok(())
    }

    // Hash operations
    #[instrument(skip(self), fields(key = %key))]
    pub async fn hkeys_str(&mut self, key: &str) -> granite::Result<Vec<String>> {
        trace!("HKEYS");
        let keys: Vec<String> = self.cx.hkeys(key).await?;
        Ok(keys)
    }

    #[instrument(skip(self), fields(key = %key))]
    pub async fn hvals_json<T>(&mut self, key: &str) -> granite::Result<Vec<T>>
    where
        T: serde::de::DeserializeOwned,
    {
        trace!("HVALS JSON");
        let values: Vec<String> = self.cx.hvals(key).await?;
        let values: Vec<T> = values
            .into_iter()
            .map(|value| serde_json::from_str(&value))
            .collect::<Result<Vec<T>, _>>()?;
        Ok(values)
    }

    #[instrument(skip(self, value), fields(key = %key, field = %field))]
    pub async fn hset_val<T>(&mut self, key: &str, field: &str, value: T) -> granite::Result<()>
    where
        T: redis::ToRedisArgs + Send + Sync,
    {
        trace!("HSET");
        self.cx.hset::<_, _, _, ()>(key, field, value).await?;
        Ok(())
    }

    #[instrument(skip(self), fields(key = %key, field = %field))]
    pub async fn hget_val<T>(&mut self, key: &str, field: &str) -> granite::Result<T>
    where
        T: redis::FromRedisValue,
    {
        trace!("HGET");
        let value: T = self.cx.hget(key, field).await?;
        Ok(value)
    }

    #[instrument(skip(self), fields(key = %key, field = %field))]
    pub async fn hget_json<T>(&mut self, key: &str, field: &str) -> granite::Result<T>
    where
        T: serde::de::DeserializeOwned,
    {
        trace!("HGET JSON");
        let value: String = self.cx.hget(key, field).await?;
        let value: T = serde_json::from_str(&value)?;
        Ok(value)
    }

    #[instrument(skip(self, value), fields(key = %key, field = %field))]
    pub async fn hset_json<T>(&mut self, key: &str, field: &str, value: &T) -> granite::Result<()>
    where
        T: serde::Serialize,
    {
        trace!("HSET JSON");
        let value = serde_json::to_string(value)?;
        self.cx.hset::<_, _, _, ()>(key, field, value).await?;
        Ok(())
    }

    // List operations
    #[instrument(skip(self), fields(key = %key, index = %index))]
    pub async fn lindex_val<T>(&mut self, key: &str, index: isize) -> granite::Result<T>
    where
        T: redis::FromRedisValue,
    {
        trace!("LINDEX");
        let value: T = self.cx.lindex(key, index).await?;
        Ok(value)
    }

    #[instrument(skip(self), fields(key = %key, index = %index))]
    pub async fn lindex_json<T>(&mut self, key: &str, index: isize) -> granite::Result<T>
    where
        T: serde::de::DeserializeOwned,
    {
        trace!("LINDEX JSON");
        let value: String = self.cx.lindex(key, index).await?;
        let value: T = serde_json::from_str(&value)?;
        Ok(value)
    }

    #[instrument(skip(self), fields(key = %key))]
    pub async fn lpop_val<T>(&mut self, key: &str) -> granite::Result<T>
    where
        T: redis::FromRedisValue,
    {
        trace!("LPOP");
        let value: T = self.cx.lpop(key, None).await?;
        Ok(value)
    }

    #[instrument(skip(self), fields(key = %key))]
    pub async fn lpop_json<T>(&mut self, key: &str) -> granite::Result<T>
    where
        T: serde::de::DeserializeOwned,
    {
        trace!("LPOP JSON");
        let value: String = self.cx.lpop(key, None).await?;
        let value: T = serde_json::from_str(&value)?;
        Ok(value)
    }

    #[instrument(skip(self, value), fields(key = %key))]
    pub async fn lpush_val<T>(&mut self, key: &str, value: T) -> granite::Result<()>
    where
        T: redis::ToRedisArgs + Send + Sync,
    {
        trace!("LPUSH");
        self.cx.lpush::<_, _, ()>(key, value).await?;
        Ok(())
    }

    #[instrument(skip(self, value), fields(key = %key))]
    pub async fn lpush_json<T>(&mut self, key: &str, value: &T) -> granite::Result<()>
    where
        T: serde::Serialize,
    {
        trace!("LPUSH JSON");
        let value = serde_json::to_string(value)?;
        self.cx.lpush::<_, _, ()>(key, value).await?;
        Ok(())
    }

    #[instrument(skip(self), fields(key = %key, start = %start, stop = %stop))]
    pub async fn lrange_val<T>(
        &mut self,
        key: &str,
        start: isize,
        stop: isize,
    ) -> granite::Result<Vec<T>>
    where
        T: redis::FromRedisValue,
    {
        trace!("LRANGE");
        let values: Vec<T> = self.cx.lrange(key, start, stop).await?;
        Ok(values)
    }

    #[instrument(skip(self), fields(key = %key, start = %start, stop = %stop))]
    pub async fn lrange_json<T>(
        &mut self,
        key: &str,
        start: isize,
        stop: isize,
    ) -> granite::Result<Vec<T>>
    where
        T: serde::de::DeserializeOwned,
    {
        trace!("LRANGE JSON");
        let values: Vec<String> = self.cx.lrange(key, start, stop).await?;
        let values: Vec<T> = values
            .into_iter()
            .map(|value| serde_json::from_str(&value))
            .collect::<Result<Vec<T>, _>>()?;
        Ok(values)
    }

    #[instrument(skip(self, value), fields(key = %key, index = %index))]
    pub async fn lset_val<T>(&mut self, key: &str, index: isize, value: T) -> granite::Result<()>
    where
        T: redis::ToRedisArgs + Send + Sync,
    {
        trace!("LSET");
        self.cx.lset::<_, _, ()>(key, index, value).await?;
        Ok(())
    }

    #[instrument(skip(self, value), fields(key = %key, index = %index))]
    pub async fn lset_json<T>(&mut self, key: &str, index: isize, value: &T) -> granite::Result<()>
    where
        T: serde::Serialize,
    {
        trace!("LSET JSON");
        let value = serde_json::to_string(value)?;
        self.cx.lset::<_, _, ()>(key, index, value).await?;
        Ok(())
    }

    #[instrument(skip(self), fields(key = %key))]
    pub async fn rpop_val<T>(&mut self, key: &str) -> granite::Result<T>
    where
        T: redis::FromRedisValue,
    {
        trace!("RPOP");
        let value: T = self.cx.rpop(key, None).await?;
        Ok(value)
    }

    #[instrument(skip(self), fields(key = %key))]
    pub async fn rpop_json<T>(&mut self, key: &str) -> granite::Result<T>
    where
        T: serde::de::DeserializeOwned,
    {
        trace!("RPOP JSON");
        let value: String = self.cx.rpop(key, None).await?;
        let value: T = serde_json::from_str(&value)?;
        Ok(value)
    }

    #[instrument(skip(self, value), fields(key = %key))]
    pub async fn rpush_val<T>(&mut self, key: &str, value: T) -> granite::Result<()>
    where
        T: redis::ToRedisArgs + Send + Sync,
    {
        trace!("RPUSH");
        self.cx.rpush::<_, _, ()>(key, value).await?;
        Ok(())
    }

    #[instrument(skip(self, value), fields(key = %key))]
    pub async fn rpush_json<T>(&mut self, key: &str, value: &T) -> granite::Result<()>
    where
        T: serde::Serialize,
    {
        trace!("RPUSH JSON");
        let value = serde_json::to_string(value)?;
        self.cx.rpush::<_, _, ()>(key, value).await?;
        Ok(())
    }

    // add sorted set operations
    #[instrument(skip(self, score, value), fields(key = %key))]
    pub async fn zadd<T>(&mut self, key: &str, score: T, value: T) -> granite::Result<()>
    where
        T: redis::ToRedisArgs + Send + Sync,
    {
        trace!("ZADD");
        self.cx.zadd::<_, _, _, ()>(key, score, value).await?;
        Ok(())
    }

    // add zrem
    #[instrument(skip(self, value), fields(key = %key))]
    pub async fn zrem<T>(&mut self, key: &str, value: T) -> granite::Result<isize>
    where
        T: redis::ToRedisArgs + Send + Sync,
    {
        trace!("ZREM");
        let value: isize = self.cx.zrem(key, value).await?;
        Ok(value)
    }

    // add zrangebyscore
    #[instrument(skip(self, min, max), fields(key = %key))]
    pub async fn zrangebyscore<T>(&mut self, key: &str, min: T, max: T) -> granite::Result<Vec<T>>
    where
        T: redis::ToRedisArgs + FromRedisValue + Send + Sync,
    {
        trace!("ZRANGEBYSCORE");
        let values: Vec<T> = self.cx.zrangebyscore(key, min, max).await?;
        Ok(values)
    }

    // add zrangebyscore_limit
    #[instrument(skip(self, min, max), fields(key = %key, offset = %offset, count = %count))]
    pub async fn zrangebyscore_limit<T, RV>(
        &mut self,
        key: &str,
        min: T,
        max: T,
        offset: isize,
        count: isize,
    ) -> granite::Result<Vec<RV>>
    where
        T: redis::ToRedisArgs + Send + Sync,
        RV: redis::FromRedisValue + Send + Sync,
    {
        trace!("ZRANGEBYSCORE_LIMIT");
        let values: Vec<RV> = self
            .cx
            .zrangebyscore_limit(key, min, max, offset, count)
            .await?;
        Ok(values)
    }

    // Counter operations
    #[instrument(skip(self, delta), fields(key = %key))]
    pub async fn incr<T>(&mut self, key: &str, delta: T) -> granite::Result<T>
    where
        T: ToRedisArgs + FromRedisValue + Send + Sync,
    {
        trace!("INCR");
        let value: T = self.cx.incr(key, delta).await?;
        Ok(value)
    }

    #[instrument(skip(self, delta), fields(key = %key))]
    pub async fn decr<T>(&mut self, key: &str, delta: T) -> granite::Result<T>
    where
        T: ToRedisArgs + FromRedisValue + Send + Sync,
    {
        trace!("DECR");
        let value: T = self.cx.decr(key, delta).await?;
        Ok(value)
    }
    #[instrument(skip(self))]
    pub async fn flushdb(&mut self) -> granite::Result<()> {
        trace!("FLUSHDB");
        let cmd = redis::cmd("FLUSHDB");
        let _ = self.cx.send_packed_command(&cmd).await?;
        Ok(())
    }

    // Set expiration in seconds
    #[instrument(skip(self), fields(key = %key, seconds = %seconds))]
    pub async fn set_expire(&mut self, key: &str, seconds: i64) -> granite::Result<bool> {
        trace!("EXPIRE");
        // Returns true if the timeout was set, false if key doesn't exist or operation was skipped
        let result: bool = self.cx.expire(key, seconds).await?;
        Ok(result)
    }
}
