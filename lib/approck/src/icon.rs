use crate::{<PERSON><PERSON>, <PERSON>Escape<PERSON>, <PERSON><PERSON>, html};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub enum Icon {
    Emoticon { symbol: String },
    Svg { url: String },
    InlineSvg(Markup),
    Image { url: String },
    InlineImage { base64_string: String },
}

impl Icon {
    pub fn emoticon(symbol: &str) -> Self {
        Icon::Emoticon {
            symbol: symbol.to_owned(),
        }
    }

    pub fn svg(url: &str) -> Self {
        Icon::Svg {
            url: url.to_owned(),
        }
    }

    pub fn inline_svg(markup: Markup) -> Self {
        Icon::InlineSvg(markup)
    }

    pub fn inline_svg_str(svg: &str) -> Self {
        Icon::InlineSvg(html! {
            (PreEscaped(svg))
        })
    }

    pub fn image(url: &str) -> Self {
        Icon::Image {
            url: url.to_owned(),
        }
    }

    pub fn inline_image_bytes(binary: &[u8]) -> Self {
        Icon::InlineImage {
            base64_string: granite::base64_encode_bytes(binary),
        }
    }

    pub fn inline_image_str(base64_string: &str) -> Self {
        Icon::InlineImage {
            base64_string: base64_string.to_owned(),
        }
    }

    pub fn emoji_plus() -> Self {
        Icon::emoticon("➕")
    }

    pub fn emoji_minus() -> Self {
        Icon::emoticon("➖")
    }

    pub fn emoji_check() -> Self {
        Icon::emoticon("✔️")
    }

    pub fn emoji_list() -> Self {
        Icon::emoticon("📋")
    }

    pub fn emoji_person() -> Self {
        Icon::emoticon("👤")
    }

    pub fn emoji_trash() -> Self {
        Icon::emoticon("🗑️")
    }

    pub fn emoji_scroll() -> Self {
        Icon::emoticon("📜")
    }

    pub fn emoji_email() -> Self {
        Icon::emoticon("✉️")
    }

    pub fn emoji_wizard() -> Self {
        Icon::emoticon("🪄")
    }
}

impl Render for Icon {
    fn render(&self) -> maud::Markup {
        match self {
            Icon::Emoticon { symbol } => html! {
                (symbol)
            },
            Icon::Svg { url } => html! {
                img src=(url);
            },
            Icon::InlineSvg(markup) => html! {
                (markup)
            },
            Icon::Image { url } => html! {
                img src=(url);
            },
            Icon::InlineImage { base64_string } => html! {
                img src=(format!("data:image/png;base64,{}", base64_string));
            },
        }
    }
}
