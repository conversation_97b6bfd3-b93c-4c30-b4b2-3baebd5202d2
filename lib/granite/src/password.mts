/**
 * Defines the structure for the password analysis result.
 */
export type PasswordAnalysis = {
    /** The entropy of the password in bits. */
    entropy: number;

    /** A normalized strength value from 0 to 100, suitable for a progress meter. */
    meter_value: number;

    /** A text string indicating the strength of the password. */
    meter_text: string;

    /** True if the password is acceptable. */
    acceptable: boolean;

    /** True if the password contains lowercase letters. */
    has_lowercase: boolean;
    /** True if the password contains uppercase letters. */
    has_uppercase: boolean;
    /** True if the password contains numbers. */
    has_numbers: boolean;
    /** True if the password contains common symbols. */
    has_symbols: boolean;
    /** True if the password contains any non-ASCII (Unicode) characters. */
    has_unicode: boolean;
};

/**
 * Calculates the entropy of a given password, representing its strength in bits,
 * and provides additional analysis for a strength meter.
 * Higher entropy means a stronger, more unpredictable password.
 *
 * Entropy (H) is calculated using the formula: H = L * log2(N), where:
 * L = length of the password
 * N = size of the character pool (number of unique possible characters found in the password)
 *
 * The character pool size (N) is determined by the presence of:
 * - Lowercase letters (a-z): 26 characters
 * - Uppercase letters (A-Z): 26 characters
 * - Numbers (0-9): 10 characters
 * - Common symbols: 32 characters (e.g., !@#$%^&*()-_=+[{]}\|;:'",<.>/?`~)
 * - Unicode characters: A significant pool is added if any non-ASCII character is detected.
 * The addition for Unicode is approximately 8617, which is derived from doubling the
 * effective bits per character of a fully utilized ASCII character set (log2(94) * 2).
 * This provides a more proportional and less "blocky" contribution for Unicode.
 *
 * @param password The password string to analyze.
 * @returns An object of type PasswordAnalysis containing entropy, meter values, and character type presence.
 */
export function password_analysis(password: string): PasswordAnalysis {
    // Initialize all analysis properties
    const analysis: PasswordAnalysis = {
        entropy: 0,
        meter_value: 0,
        meter_text: "Create a new password.",
        acceptable: false,
        has_lowercase: false,
        has_uppercase: false,
        has_numbers: false,
        has_symbols: false,
        has_unicode: false,
    };

    if (!password || password.length === 0) {
        return analysis; // Return initial analysis for empty password
    }

    // Define character sets
    const LOWERCASE_CHARS = "abcdefghijklmnopqrstuvwxyz";
    const UPPERCASE_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const NUMERIC_CHARS = "0123456789";
    const SYMBOL_CHARS = "!@#$%^&*()-_=+[{]}\\|;:'\",<.>/?`~"; // 32 common symbols
    // Derived from (Math.pow(2, Math.log2(26+26+10+32) * 2)) approx (2^6.55 * 2) = 2^13.1 = 8617
    const UNICODE_POOL_ADDITION = 8617;

    // Iterate through the password to determine which character categories are present
    for (const char of password) {
        // Check if the character is within ASCII range (0-127) for optimization
        const char_code = char.charCodeAt(0);

        if (char_code >= 0x20 && char_code <= 0x7E) { // Common ASCII printable characters
            if (LOWERCASE_CHARS.includes(char)) {
                analysis.has_lowercase = true;
            } else if (UPPERCASE_CHARS.includes(char)) {
                analysis.has_uppercase = true;
            } else if (NUMERIC_CHARS.includes(char)) {
                analysis.has_numbers = true;
            } else if (SYMBOL_CHARS.includes(char)) {
                analysis.has_symbols = true;
            }
        } else {
            // If it's not a common ASCII character, consider it as a Unicode character
            analysis.has_unicode = true;
        }
    }

    let character_pool_size = 0;

    // Sum the size of the character sets found in the password
    if (analysis.has_lowercase) {
        character_pool_size += LOWERCASE_CHARS.length; // 26
    }
    if (analysis.has_uppercase) {
        character_pool_size += UPPERCASE_CHARS.length; // 26
    }
    if (analysis.has_numbers) {
        character_pool_size += NUMERIC_CHARS.length; // 10
    }
    if (analysis.has_symbols) {
        character_pool_size += SYMBOL_CHARS.length; // 32
    }
    if (analysis.has_unicode) {
        // Add a significant number to the pool if Unicode characters are present.
        character_pool_size += UNICODE_POOL_ADDITION;
    }

    // If no recognized character types are found (e.g., password contains only spaces or unrecognized chars),
    // or if the password length is 0, the entropy is 0.
    if (character_pool_size === 0) {
        return analysis; // Return initial analysis with 0 entropy
    }

    // Calculate entropy: L * log2(N)
    analysis.entropy = password.length * (Math.log2(character_pool_size));

    // --- Calculate meter_value and meter_color based on entropy ---
    const max_entropy_for_meter = 140; // Set a cap for the meter to max out (e.g., around 140 bits)
    analysis.meter_value = Math.min(100, (analysis.entropy / max_entropy_for_meter) * 100);
    analysis.acceptable = analysis.entropy >= 60;

    // Define entropy thresholds for meter color
    if (analysis.entropy < 28) {
        analysis.meter_text = "⚠️⚠️ Very Weak.  Add more characters.";
    } else if (analysis.entropy < 60) {
        analysis.meter_text = "⚠️ Weak. Add more character types.";
    } else if (analysis.entropy < 90) {
        analysis.meter_text = "✅ Good. More characters increase strength.";
    } else if (analysis.entropy < 120) {
        analysis.meter_text = "✅✅ Strong.  You are good to go.";
    } else {
        analysis.meter_text = "✅✅✅ Very Strong.  Excellent!";
    }

    return analysis;
}

// --- Example Usage ---
// To use this function, you would call it like this:
// const password_1 = "MyStrongP4$$word!";
// const analysis_1 = password_analysis(password_1);
// console.log(`Analysis for "${password_1}":`);
// console.log(analysis_1);

// const password_2 = "password";
// const analysis_2 = password_analysis(password_2);
// console.log(`Analysis for "${password_2}":`);
// console.log(analysis_2);

// const password_3 = "12345";
// const analysis_3 = password_analysis(password_3);
// console.log(`Analysis for "${password_3}":`);
// console.log(analysis_3);

// const password_4 = "";
// const analysis_4 = password_analysis(password_4);
// console.log(`Analysis for "${password_4}":`);
// console.log(analysis_4);

// const password_5 = "short"; // lowercase only, short
// const analysis_5 = password_analysis(password_5);
// console.log(`Analysis for "${password_5}":`);
// console.log(analysis_5);

// const password_6 = "VeryLongAndComplexPassword123!@#$"; // all types, long
// const analysis_6 = password_analysis(password_6);
// console.log(`Analysis for "${password_6}":`);
// console.log(analysis_6);

// New example with Unicode:
// const password_7 = "こんにちは世界123!"; // Japanese characters + numbers + symbol
// const analysis_7 = password_analysis(password_7);
// console.log(`Analysis for "${password_7}":`);
// console.log(analysis_7);

// const password_8 = "éàüöç"; // Extended Latin characters
// const analysis_8 = password_analysis(password_8);
// console.log(`Analysis for "${password_8}":`);
// console.log(analysis_8);
