#[approck::http(GET /admin/packet-type/{admin_uuid:Uuid}/edit; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Packet Type Edit");

        let mut panel = bux::component::save_cancel_form_panel(
            "",
            "/admin/packet-type/00000000-0000-0000-0000-000000000000/",
        );
        #[rustfmt::skip]
        panel.add_body(maud::html!(
            (bux::input::text::string::name_label_value_help("packet_name", "Packet Name", None, "Example: 2024 R&D Tax Credit"))
            (bux::input::date::bux_input_date("due_date", "Due Date", None))
            (bux::input::textarea::string::name_label_value("description", "Description", None))
            h5 { "Select Items for the Packet" }
            (bux::input::checkbox::name_label_checked("wages", "Wages", false))
            (bux::input::checkbox::name_label_checked("supplies", "Supplies", false))
            (bux::input::checkbox::name_label_checked("subcontractors", "Subcontractors", false))
            (bux::input::checkbox::name_label_checked("others", "Others", false))
        ));
        doc.add_body(html! {
            bux-action-panel {
                (panel)
            }
        });
        Response::HTML(doc.into())
    }
}
