-- RRR initial data

-- RRR admin permissions for all identities
INSERT INTO rrr_admin.identity (identity_uuid, perm_web_usage, perm_api_usage, perm_agent_read, perm_agent_write) 
SELECT identity_uuid, true, true, true, true FROM auth_fence.identity;

-- Legal documents
INSERT INTO legal_plane.document (document_uuid, document_psid, active, name, body_markdown, revision) VALUES
(public.uuidv7(), 'AgentAgreement', true, 'RRR Agent Agreement', '# Lorem Ipsum Agent Terms

## Lorem Overview

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore:

## 1. Lorem Responsibilities

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut enim ad minim veniam:

- Lorem ipsum dolor sit amet consectetur
- Adipiscing elit sed do eiusmod tempor
- Incididunt ut labore et dolore magna
- Aliqua ut enim ad minim veniam

## 2. Lorem Structure

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor:

- Lorem ipsum dolor sit amet consectetur adipiscing
- Elit sed do eiusmod tempor incididunt
- Ut labore et dolore magna aliqua
- Enim ad minim veniam quis nostrud

## 3. Lorem Requirements

Lorem ipsum dolor sit amet, consectetur adipiscing elit:

- Lorem ipsum dolor sit amet consectetur
- Adipiscing elit sed do eiusmod
- Tempor incididunt ut labore et dolore
- Magna aliqua ut enim ad minim

## 4. Lorem Confidentiality

Lorem ipsum dolor sit amet, consectetur adipiscing elit:

- Lorem ipsum dolor sit amet consectetur
- Adipiscing elit sed do eiusmod tempor
- Incididunt ut labore et dolore magna
- Aliqua ut enim ad minim veniam

## 5. Lorem Termination

Lorem ipsum dolor sit amet, consectetur adipiscing elit:

- Lorem ipsum dolor sit amet consectetur
- Adipiscing elit sed do eiusmod tempor
- Incididunt ut labore et dolore magna
- Aliqua ut enim ad minim veniam

## 6. Lorem Liability

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt.

## 7. Lorem Law

Lorem ipsum dolor sit amet, consectetur adipiscing elit.

---

**Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore.**', '1.0');

-- Legal document version history
INSERT INTO legal_plane.document_version (document_version_uuid, document_uuid, name, body_markdown, revision)
SELECT public.uuidv7(), document_uuid, name, body_markdown, revision
FROM legal_plane.document 
WHERE document_psid = 'AgentAgreement';
