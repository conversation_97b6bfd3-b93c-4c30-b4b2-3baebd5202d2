#!/bin/bash
# Ensure db-start.sh has already run and PostgreSQL is ready

set -e

# Enable extended glob patterns
shopt -s extglob

cd "$(dirname "$0")"

## runpod ###########################################

echo -e "Attempting to create candoc database"
docker compose exec postgres createdb -U app --owner app candoc
echo -e "candoc database created: Loading candoc...\n"
cat ./public-*.schema.sql \
    ./auth-fence-+([0-9]).schema.sql \
    ./auth-fence.data.sql \
    ./intake-silo-*.schema.sql \
    ./candoc-*.schema.sql \
    ./candoc.data.sql | docker compose exec -T postgres psql -U app -d candoc --echo-errors -v ON_ERROR_STOP=1

echo -e "\ncandoc load complete\n"

## runpod ###########################################

echo -e "Attempting to create runpod database"
docker compose exec postgres createdb -U app --owner app runpod

echo -e "runpod database created: Loading runpod...\n"
cat ./public-*.schema.sql \
    ./auth-fence-+([0-9]).schema.sql \
    ./auth-fence.data.sql \
    ./intake-silo-*.schema.sql \
    ./runpod-*.schema.sql \
    ./runpod.data.sql | docker compose exec -T postgres psql -U app -d runpod --echo-errors -v ON_ERROR_STOP=1

echo -e "\nrunpod load complete\n"

## reo ##############################################

echo -e "Attempting to create reo database"
docker compose exec postgres createdb -U app --owner app reo

echo -e "reo database created: Loading reo...\n"
cat ./public-*.schema.sql \
    ./auth-fence-+([0-9]).schema.sql \
    ./auth-fence.data.sql \
    ./intake-silo-*.schema.sql \
    ./reo-*.schema.sql \
    ./reo.data.sql | docker compose exec -T postgres psql -U app -d reo --echo-errors -v ON_ERROR_STOP=1

echo -e "\nreo load complete\n"

## df4l #############################################

echo -e "Attempting to create df4l database"
docker compose exec postgres createdb -U app --owner app df4l

echo -e "df4l database created: Loading df4l...\n"
cat ./public-*.schema.sql \
    ./addr-iso-*.schema.sql \
    ./auth-fence-+([0-9]).schema.sql \
    ./auth-fence.data.sql \
    ./auth-fence-provider-+([0-9]).schema.sql \
    ./auth-fence-provider.data.sql \
    ./intake-silo-*.schema.sql \
    ./legal-plane-*.schema.sql \
    ./api-twilio-*.schema.sql \
    ./api-sendgrid-*.schema.sql \
    ./df4l-*.schema.sql \
    ./df4l.data.sql | docker compose exec -T postgres psql -U app -d df4l --echo-errors -v ON_ERROR_STOP=1

echo -e "\ndf4l load complete\n"

## gifthopper #############################################

echo -e "Attempting to create gifthopper database"
docker compose exec postgres createdb -U app --owner app gifthopper

echo -e "gifthopper database created: Loading gifthopper...\n"
cat ./public-*.schema.sql \
    ./auth-fence-+([0-9]).schema.sql \
    ./auth-fence.data.sql \
    ./intake-silo-*.schema.sql \
    ./gifthopper-*.schema.sql \
    ./gifthopper.data.sql | docker compose exec -T postgres psql -U app -d gifthopper --echo-errors -v ON_ERROR_STOP=1

echo -e "\ngifthopper load complete\n"


## homewaters #############################################

echo -e "Attempting to create homewaters database"
docker compose exec postgres createdb -U app --owner app homewaters

echo -e "homewaters database created: Loading Homewaters...\n"
cat ./public-*.schema.sql \
    ./auth-fence-+([0-9]).schema.sql \
    ./auth-fence.data.sql \
    ./intake-silo-*.schema.sql \
    ./homewaters-*.schema.sql \
    ./homewaters.data.sql | docker compose exec -T postgres psql -U app -d homewaters --echo-errors -v ON_ERROR_STOP=1

echo -e "\nhomewaters load complete\n"



## rrr #############################################

echo -e "Attempting to create rrr database"
docker compose exec postgres createdb -U app --owner app rrr

echo -e "rrr database created: Loading rrr...\n"
cat ./public-*.schema.sql \
    ./addr-iso-*.schema.sql \
    ./auth-fence-+([0-9]).schema.sql \
    ./auth-fence.data.sql \
    ./intake-silo-*.schema.sql \
    ./legal-plane-*.schema.sql \
    ./api-twilio-*.schema.sql \
    ./api-sendgrid-*.schema.sql \
    ./rrr-*.schema.sql \
    ./rrr.data.sql | docker compose exec -T postgres psql -U app -d rrr --echo-errors -v ON_ERROR_STOP=1

echo -e "\nrrr load complete\n"

## pm5 #############################################

echo -e "Attempting to create pm5 database"
docker compose exec postgres createdb -U app --owner app pm5

echo -e "pm5 database created: Loading pm5...\n"
cat ./public-*.schema.sql \
    ./auth-fence-+([0-9]).schema.sql \
    ./auth-fence.data.sql \
    ./intake-silo-*.schema.sql \
    ./pm5-*.schema.sql \
    ./pm5.data.sql | docker compose exec -T postgres psql -U app -d pm5 --echo-errors -v ON_ERROR_STOP=1

echo -e "\npm5 load complete\n"
