#[approck::api]
pub mod client_budget_get {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub budget_extra_debts: Option<Decimal>,
        pub budget_extra_savings: Option<Decimal>,
        pub budget_extra_retirement: Option<Decimal>,
        pub budget_extra_surplus: Option<Decimal>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let wizard = crate::core::client::wizard::Wizard::load(&dbcx, input.client_uuid).await?;
        let row = wizard.budget_input;

        Ok(Output {
            budget_extra_debts: row.budget_extra_debts,
            budget_extra_savings: row.budget_extra_savings,
            budget_extra_retirement: row.budget_extra_retirement,
            budget_extra_surplus: row.budget_extra_surplus,
        })
    }
}

#[approck::api]
pub mod client_budget_set {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub budget_extra_debts: Option<Decimal>,
        pub budget_extra_savings: Option<Decimal>,
        pub budget_extra_retirement: Option<Decimal>,
        pub budget_extra_surplus: Option<Decimal>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {}

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        granite::pg_execute!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
                $budget_extra_debts: &input.budget_extra_debts,
                $budget_extra_savings: &input.budget_extra_savings,
                $budget_extra_retirement: &input.budget_extra_retirement,
                $budget_extra_surplus: &input.budget_extra_surplus,
            };
            UPDATE
                df4l.client
            SET
                budget_extra_debts = $budget_extra_debts,
                budget_extra_savings = $budget_extra_savings,
                budget_extra_retirement = $budget_extra_retirement,
                budget_extra_surplus = $budget_extra_surplus
            WHERE
                client_uuid = $client_uuid
        )
        .await?;

        Ok(Output {})
    }
}

#[approck::api]
pub mod client_budget_validate {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub enum Output {
        Valid,
        Invalid {
            message: String,
            budget_extra_debts: Option<String>,
            budget_extra_savings: Option<String>,
            budget_extra_retirement: Option<String>,
            budget_extra_surplus: Option<String>,
        },
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        let wizard = crate::core::client::wizard::Wizard::load(&dbcx, input.client_uuid).await?;

        match wizard.budget_result {
            Ok(_) => Ok(Output::Valid),
            Err(budget_error) => Ok(Output::Invalid {
                message: budget_error.message,
                budget_extra_debts: budget_error.budget_extra_debts,
                budget_extra_savings: budget_error.budget_extra_savings,
                budget_extra_retirement: budget_error.budget_extra_retirement,
                budget_extra_surplus: budget_error.budget_extra_surplus,
            }),
        }
    }
}
