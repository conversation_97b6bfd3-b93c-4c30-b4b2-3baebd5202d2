pub mod budget;
pub mod contact;
pub mod crs;
pub mod debt;
pub mod icover;
pub mod report;

#[approck::api]
pub mod client_wizard_context {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client_uuid: Uuid,
        pub advisor_uuid: Uuid,
        pub contact_complete: bool,
        pub crs_complete: bool,
        pub debt_complete: bool,
        pub budget_complete: bool,
        pub report_complete: bool,
        pub icover_complete: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let wizard = crate::core::client::wizard::Wizard::load(&dbcx, input.client_uuid).await?;

        Ok(Output {
            client_uuid: wizard.client_uuid,
            advisor_uuid: wizard.advisor_uuid,
            contact_complete: wizard.contact_result.is_ok(),
            crs_complete: wizard.crs_result.is_ok(),
            debt_complete: wizard.debt_result.is_ok(),
            budget_complete: wizard.budget_result.is_ok(),
            report_complete: false,
            icover_complete: wizard.icover_result.is_ok(),
        })
    }
}
