#[approck::api]
pub mod client_wizard_contact_get {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client_uuid: Uuid,
        pub first_name: String,
        pub last_name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub address1: Option<String>,
        pub address2: Option<String>,
        pub city: Option<String>,
        pub state: Option<String>,
        pub zip: Option<String>,
        pub country: Option<String>,
        pub note: Option<String>,
        pub gender: Option<String>,
        pub birth_date: Option<DateUtc>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
            };
            row = {
                client_uuid: Uuid,
                first_name: String,
                last_name: String,
                email: Option<String>,
                phone: Option<String>,
                address1: Option<String>,
                address2: Option<String>,
                city: Option<String>,
                state: Option<String>,
                zip: Option<String>,
                country: Option<String>,
                gender: Option<String>,
                birth_date: Option<DateUtc>,
                note: Option<String>,
            };
            SELECT
                client_uuid,
                first_name,
                last_name,
                email,
                phone,
                address1,
                address2,
                city,
                state,
                zip,
                country,
                gender,
                birth_date,
                note
            FROM
                df4l.client
            WHERE
                client_uuid = $client_uuid
        )
        .await?;

        Ok(Output {
            client_uuid: row.client_uuid,
            first_name: row.first_name,
            last_name: row.last_name,
            email: row.email,
            phone: row.phone,
            address1: row.address1,
            address2: row.address2,
            city: row.city,
            state: row.state,
            zip: row.zip,
            country: row.country,
            gender: row.gender,
            birth_date: row.birth_date,
            note: row.note,
        })
    }
}

#[approck::api]
pub mod client_wizard_contact_set {
    use granite::NestedError;
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub first_name: String,
        pub last_name: String,
        pub email: String,
        pub phone: String,
        pub address1: String,
        pub address2: Option<String>,
        pub city: String,
        pub state: String,
        pub zip: String,
        pub gender: String,
        pub birth_date: DateUtc,
        pub note: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output;

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        let mut error = Input_Error {
            client_uuid: None,
            first_name: None,
            last_name: None,
            email: None,
            phone: None,
            address1: None,
            address2: None,
            city: None,
            state: None,
            zip: None,
            gender: None,
            birth_date: None,
            note: None,
        };

        let first_name = input.first_name.trim().to_string();
        let last_name = input.last_name.trim().to_string();
        let email = input.email.trim().to_string();
        let phone = input.phone.trim().to_string();
        let address1 = input.address1.trim().to_string();
        let address2 = input
            .address2
            .map(|s| s.trim().to_string())
            .and_then(|v| if v.is_empty() { None } else { Some(v) });
        let city = input.city.trim().to_string();
        let state = input.state.trim().to_string();
        let zip = input.zip.trim().to_string();
        let gender = input.gender.trim().to_string();
        let birth_date = input.birth_date;
        let note = input
            .note
            .map(|s| s.trim().to_string())
            .and_then(|v| if v.is_empty() { None } else { Some(v) });

        if first_name.is_empty() {
            error.first_name = Some("First name is required.".to_string());
        }
        if last_name.is_empty() {
            error.last_name = Some("Last name is required.".to_string());
        }
        if email.is_empty() {
            error.email = Some("Email is required.".to_string());
        }
        if phone.is_empty() {
            error.phone = Some("Phone is required.".to_string());
        }
        if address1.is_empty() {
            error.address1 = Some("Address is required.".to_string());
        }
        if city.is_empty() {
            error.city = Some("City is required.".to_string());
        }
        if state.is_empty() {
            error.state = Some("State is required.".to_string());
        }
        if zip.is_empty() {
            error.zip = Some("Zip is required.".to_string());
        }
        if gender.is_empty() {
            error.gender = Some("Gender is required.".to_string());
        }
        if input.birth_date.to_string().is_empty() {
            error.birth_date = Some("Birth date is required.".to_string());
        }

        if error.first_name.is_some()
            || error.last_name.is_some()
            || error.email.is_some()
            || error.phone.is_some()
            || error.address1.is_some()
            || error.city.is_some()
            || error.state.is_some()
            || error.zip.is_some()
            || error.gender.is_some()
            || error.birth_date.is_some()
        {
            return Ok(Response::ValidationError(NestedError {
                outer: "Validation Error".to_string(),
                inner: Some(error),
            }));
        }

        granite::pg_execute!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
                $first_name: &first_name,
                $last_name: &last_name,
                $email: &email,
                $phone: &phone,
                $address1: &address1,
                $address2: &address2,
                $city: &city,
                $state: &state,
                $zip: &zip,
                $note: &note,
                $gender: &gender,
                $birth_date: &birth_date,
            };
            UPDATE
                df4l.client
            SET
                first_name = $first_name,
                last_name = $last_name,
                email = $email,
                phone = $phone,
                address1 = $address1,
                address2 = $address2,
                city = $city,
                state = $state,
                zip = $zip,
                gender = $gender,
                birth_date = $birth_date,
                note = $note
            WHERE
                client_uuid = $client_uuid
        )
        .await?;

        Ok(Response::Output(Output))
    }
}
