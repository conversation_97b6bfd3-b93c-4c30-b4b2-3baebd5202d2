#[approck::api]
pub mod client_icover_get {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub enum Output {
        Ready { icover_url: String },
        NotReady { messages: Vec<String> },
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let wizard = crate::core::client::wizard::Wizard::load(&dbcx, input.client_uuid).await?;

        match wizard.icover_result {
            Ok(icover_output) => Ok(Output::Ready {
                icover_url: icover_output.icover_url,
            }),
            Err(icover_error) => Ok(Output::NotReady {
                messages: icover_error.messages,
            }),
        }
    }
}
