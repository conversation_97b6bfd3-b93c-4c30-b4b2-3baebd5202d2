#[approck::api]
pub mod client_add {
    use granite::ResultExt;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
        pub first_name: String,
        pub last_name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub note: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client_uuid: Uuid,
        pub onboarding_url: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let mut dbcx = app.postgres_dbcx().await?;

        if !identity.client_add(input.advisor_uuid) {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to client add".to_string(),
            ));
        }

        let mut error = Input_Error {
            advisor_uuid: None,
            first_name: None,
            last_name: None,
            email: None,
            phone: None,
            note: None,
        };

        // Validate input
        let first_name = input.first_name.trim().to_string();
        let last_name = input.last_name.trim().to_string();
        let email = input
            .email
            .map(|s| s.trim().to_string())
            .and_then(|v| if v.is_empty() { None } else { Some(v) });
        let phone = input
            .phone
            .map(|s| s.trim().to_string())
            .and_then(|v| if v.is_empty() { None } else { Some(v) });
        let note = input
            .note
            .map(|s| s.trim().to_string())
            .and_then(|v| if v.is_empty() { None } else { Some(v) });

        if first_name.is_empty() {
            error.first_name = Some("First name is required.".to_string());
        }
        if last_name.is_empty() {
            error.last_name = Some("Last name is required.".to_string());
        }

        if error.first_name.is_some() || error.last_name.is_some() {
            return Ok(Response::ValidationError(granite::NestedError {
                outer: "Validation Error".to_string(),
                inner: Some(error),
            }));
        }

        // Transaction block
        let row = {
            let dbtx = dbcx
                .transaction()
                .await
                .amend(|e| e.add_context("starting transaction"))?;

            let row = granite::pg_row!(
                db = dbtx;
                args = {
                    $advisor_uuid: &input.advisor_uuid,
                    $first_name: &first_name,
                    $last_name: &last_name,
                    $email: &email,
                    $phone: &phone,
                    $note: &note,
                };
                row = {
                    client_uuid: Uuid,
                };

                INSERT INTO
                    df4l.client
                    (
                        advisor_uuid,
                        first_name,
                        last_name,
                        email,
                        phone,
                        note
                    )
                VALUES
                    (
                        $advisor_uuid,
                        $first_name,
                        $last_name,
                        $email,
                        $phone,
                        $note
                    )
                RETURNING
                    client_uuid
            )
            .await?;

            dbtx.commit()
                .await
                .amend(|e| e.add_context("committing transaction"))?;
            row
        };

        Ok(Response::Output(Output {
            client_uuid: row.client_uuid,
            onboarding_url: crate::ml_advisor_client_wizard(input.advisor_uuid, row.client_uuid),
        }))
    }
}
