#[approck::api]
pub mod advisor_client_debt_list {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub keyword: Option<String>,
        pub active: Option<bool>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub debt_list: Vec<Debt>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Debt {
        pub client_debt_uuid: Uuid,
        pub advisor_uuid: Option<Uuid>,
        pub client_uuid: Uuid,
        pub name: String,
        pub balance: Option<Decimal>,
        pub interest_rate: Option<Decimal>,
        pub monthly_payment: Option<Decimal>,
        pub active: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
                $active: &input.active,
                $keyword: &input.keyword,
            };
            row = {
                client_debt_uuid: Uuid,
                client_uuid: Uuid,
                advisor_uuid: Option<Uuid>,
                name: String,
                balance: Option<Decimal>,
                interest_rate: Option<Decimal>,
                monthly_payment: Option<Decimal>,
                active: bool,
            };
            SELECT
                cd.client_debt_uuid,
                cd.client_uuid,
                c.advisor_uuid,
                cd.name,
                cd.balance,
                cd.interest_rate,
                cd.monthly_payment,
                cd.active
            FROM
                df4l.client0_debt AS cd
            JOIN
                df4l.client0 AS c on c.client_uuid = cd.client_uuid
            WHERE true
                AND cd.client_uuid = $client_uuid::uuid
                AND ($keyword::text IS NULL OR cd.name ILIKE "%" || $keyword::text || "%")
                AND ($active::bool IS NULL OR cd.active = $active::bool)
            ORDER BY
                balance, client_debt_uuid  --smallest first, deterministic

        )
        .await?;

        Ok(Output {
            debt_list: rows
                .into_iter()
                .map(|r| Debt {
                    client_debt_uuid: r.client_debt_uuid,
                    client_uuid: r.client_uuid,
                    advisor_uuid: r.advisor_uuid,
                    name: r.name,
                    balance: r.balance,
                    interest_rate: r.interest_rate,
                    monthly_payment: r.monthly_payment,
                    active: r.active,
                })
                .collect(),
        })
    }
}
