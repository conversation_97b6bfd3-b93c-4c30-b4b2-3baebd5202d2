//! This file is powered off of the df4l.client table
//! SECON checks must be done before calling this functionality.

use granite::Decimal;
use std::collections::HashMap;

//-------------------------------------------------------------------------------------------------

pub struct Wizard {
    pub client_uuid: granite::Uuid,
    pub advisor_uuid: granite::Uuid,
    pub advisor_result: Result<AdvisorOutput, AdvisorError>,
    pub contact_partial: Contact_Partial,
    pub contact_result: Result<Contact, Contact_Error>,
    pub crs_result: Result<Crs, Crs_Error>,
    pub debt_input: DebtInput,
    pub debt_result: Result<DebtOutput, DebtError>,
    pub budget_input: BudgetInput,
    pub budget_result: Result<BudgetOutput, BudgetError>,
    pub icover_result: Result<ICoverOutput, ICoverError>,
}

impl Wizard {
    pub async fn load(
        db: &impl approck_postgres::DB,
        client_uuid: granite::Uuid,
    ) -> granite::Result<Wizard> {
        // query to get all the data
        let row = granite::pg_row!(
            db = db;
            args = {
                $client_uuid: &client_uuid,
            };
            row = {
                advisor_uuid: Uuid,
                advisor_gbu_esid: Option<String>,
                advisor_first_name: String,
                advisor_last_name: String,
                advisor_address_1: Option<String>,
                advisor_address_2: Option<String>,
                advisor_city: Option<String>,
                advisor_state: Option<String>,
                advisor_zip: Option<String>,
                advisor_email: Option<String>,
                advisor_phone: Option<String>,
                advisor_statelic: Vec<String>,
                first_name: Option<String>,
                last_name: Option<String>,
                email: Option<String>,
                phone: Option<String>,
                address1: Option<String>,
                address2: Option<String>,
                city: Option<String>,
                state: Option<String>,
                zip: Option<String>,
                budget_extra_debts: Option<Decimal>,
                budget_extra_savings: Option<Decimal>,
                budget_extra_retirement: Option<Decimal>,
                budget_extra_surplus: Option<Decimal>,
            };
            SELECT
                advisor.advisor_uuid,
                advisor.gbu_advisor_esid,
                advisor.first_name,
                advisor.last_name,
                advisor.address1,
                advisor.address2,
                advisor.city,
                advisor.state,
                advisor.zip,
                advisor.email,
                advisor.phone,
                COALESCE((
                    SELECT
                        array_agg(state_code)
                    FROM
                        df4l.advisor_statelic
                    WHERE
                        advisor_uuid = advisor.advisor_uuid
                ), ARRAY[]::text[]) AS advisor_statelic,
                client.first_name,
                client.last_name,
                client.email,
                client.phone,
                client.address1,
                client.address2,
                client.city,
                client.state,
                client.zip,
                client.budget_extra_debts,
                client.budget_extra_savings,
                client.budget_extra_retirement,
                client.budget_extra_surplus
            FROM
                df4l.client
                INNER JOIN df4l.advisor ON advisor.advisor_uuid = client.advisor_uuid
            WHERE
                client.client_uuid = $client_uuid
        )
        .await?;

        let advisor_partial = AdvisorPartial {
            advisor_uuid: row.advisor_uuid,
            gbu_esid: row.advisor_gbu_esid,
            first_name: row.advisor_first_name,
            last_name: row.advisor_last_name,
            address1: row.advisor_address_1,
            address2: row.advisor_address_2,
            city: row.advisor_city,
            state: row.advisor_state,
            zip: row.advisor_zip,
            email: row.advisor_email,
            phone: row.advisor_phone,
            statelic: row.advisor_statelic,
        };

        let advisor_result = advisor_partial.validate();

        let contact_partial = Contact_Partial {
            first_name: row.first_name,
            last_name: row.last_name,
            email: row.email,
            phone: row.phone,
            address1: row.address1,
            address2: Some(row.address2),
            city: row.city,
            state: row.state,
            zip: row.zip,
        };

        let contact_result = contact_partial.validate();

        // get merged debts
        let merged_debts = df4l_crs::core::get_client_debts_merged(db, client_uuid).await?;
        let debt_input = DebtInput {
            debts: merged_debts
                .debts
                .into_iter()
                .map(|debt| DebtRowInput {
                    client_debt_uuid: debt.client_debt_uuid,
                    is_external: debt.client_debt_esid.is_some(),
                    name: debt.name,
                    balance: debt.balance,
                    balance_date: debt.balance_date,
                    interest: debt.interest,
                    payment: debt.payment,
                    active: debt.active,
                })
                .collect(),
        };

        let debt_result = debt_input.validate();

        let budget_input = BudgetInput {
            budget_extra_debts: row.budget_extra_debts,
            budget_extra_savings: row.budget_extra_savings,
            budget_extra_retirement: row.budget_extra_retirement,
            budget_extra_surplus: row.budget_extra_surplus,
        };

        let budget_result = budget_input.validate();

        let icover_result = {
            let mut errors = Vec::new();

            if let Err(ref e) = advisor_result {
                errors.push("Your advisor setup is incomplete.".to_string());
                if let Some(ref e) = e.gbu_esid {
                    errors.push(e.clone());
                }
                if let Some(ref e) = e.first_name {
                    errors.push(e.clone());
                }
                if let Some(ref e) = e.last_name {
                    errors.push(e.clone());
                }
                if let Some(ref e) = e.email {
                    errors.push(e.clone());
                }
                if let Some(ref e) = e.phone {
                    errors.push(e.clone());
                }
                if let Some(ref e) = e.address1 {
                    errors.push(e.clone());
                }
                if let Some(ref e) = e.city {
                    errors.push(e.clone());
                }
                if let Some(ref e) = e.state {
                    errors.push(e.clone());
                }
                if let Some(ref e) = e.zip {
                    errors.push(e.clone());
                }
                if let Some(ref e) = e.statelic {
                    errors.push(e.clone());
                }
            }

            if contact_result.is_err() {
                errors.push("Your client's personal information setup is incomplete.".to_string());
            }

            if debt_result.is_err() {
                errors.push("Your client's debt information setup is incomplete.".to_string());
            }

            if budget_result.is_err() {
                errors.push("Your client's budget information setup is incomplete.".to_string());
            }

            if !errors.is_empty() {
                Err(ICoverError { messages: errors })
            } else {
                Ok(ICoverOutput {
                    icover_url: format!(
                        "https://gbu-local-development.icoverdemo.com/?client_uuid={}",
                        client_uuid
                    ),
                })
            }
        };

        Ok(Wizard {
            client_uuid,
            advisor_uuid: row.advisor_uuid,
            advisor_result,
            contact_partial,
            contact_result,
            crs_result: Crs {}.validate(),
            debt_input,
            debt_result,
            budget_input,
            budget_result,
            icover_result,
        })
    }
}

#[granite::gtype]
pub struct AdvisorOutput {
    pub advisor_uuid: Uuid,
    pub gbu_esid: String,
    pub first_name: String,
    pub last_name: String,
    pub address1: String,
    pub address2: String,
    pub city: String,
    pub state: String,
    pub zip: String,
    pub email: String,
    pub phone: String,
    pub statelic: Vec<String>,
}

#[granite::gtype]
pub struct AdvisorPartial {
    pub advisor_uuid: Uuid,
    pub gbu_esid: Option<String>,
    pub first_name: String,
    pub last_name: String,
    pub address1: Option<String>,
    pub address2: Option<String>,
    pub city: Option<String>,
    pub state: Option<String>,
    pub zip: Option<String>,
    pub email: Option<String>,
    pub phone: Option<String>,
    pub statelic: Vec<String>,
}

#[granite::gtype]
pub struct AdvisorError {
    pub advisor_uuid: Uuid,
    pub gbu_esid: Option<String>,
    pub first_name: Option<String>,
    pub last_name: Option<String>,
    pub address1: Option<String>,
    pub address2: Option<String>,
    pub city: Option<String>,
    pub state: Option<String>,
    pub zip: Option<String>,
    pub email: Option<String>,
    pub phone: Option<String>,
    pub statelic: Option<String>,
}

impl AdvisorPartial {
    #[allow(clippy::result_large_err)]
    pub fn validate(self) -> Result<AdvisorOutput, AdvisorError> {
        let mut errors = AdvisorError {
            advisor_uuid: self.advisor_uuid,
            gbu_esid: None,
            first_name: None,
            last_name: None,
            address1: None,
            address2: None,
            city: None,
            state: None,
            zip: None,
            email: None,
            phone: None,
            statelic: None,
        };

        let gbu_esid = self.gbu_esid.clone().unwrap_or_default().trim().to_string();
        let first_name = self.first_name.trim().to_string();
        let last_name = self.last_name.trim().to_string();
        let address1 = self.address1.unwrap_or_default().trim().to_string();
        let address2 = self.address2.unwrap_or_default().trim().to_string();
        let city = self.city.unwrap_or_default().trim().to_string();
        let state = self.state.unwrap_or_default().trim().to_string();
        let zip = self.zip.unwrap_or_default().trim().to_string();
        let email = self.email.unwrap_or_default().trim().to_string();
        let phone = self.phone.unwrap_or_default().trim().to_string();
        let statelic = self.statelic;

        if gbu_esid.is_empty() {
            errors.gbu_esid = Some("GBU Advisor ID is required.".to_string());
        }
        if first_name.is_empty() {
            errors.first_name = Some("First name is required.".to_string());
        }
        if last_name.is_empty() {
            errors.last_name = Some("Last name is required.".to_string());
        }
        if email.is_empty() {
            errors.email = Some("Email is required.".to_string());
        }
        if phone.is_empty() {
            errors.phone = Some("Phone is required.".to_string());
        }
        if address1.is_empty() {
            errors.address1 = Some("Address is required.".to_string());
        }
        if city.is_empty() {
            errors.city = Some("City is required.".to_string());
        }
        if state.is_empty() {
            errors.state = Some("State is required.".to_string());
        }
        if zip.is_empty() {
            errors.zip = Some("Zip is required.".to_string());
        }
        if email.is_empty() {
            errors.email = Some("Email is required.".to_string());
        }
        if phone.is_empty() {
            errors.phone = Some("Phone is required.".to_string());
        }
        if statelic.is_empty() {
            errors.statelic = Some("State Licensing selection is required.".to_string());
        }

        if errors.first_name.is_some()
            || errors.last_name.is_some()
            || errors.email.is_some()
            || errors.phone.is_some()
            || errors.address1.is_some()
            || errors.city.is_some()
            || errors.state.is_some()
            || errors.zip.is_some()
            || errors.email.is_some()
            || errors.phone.is_some()
            || errors.statelic.is_some()
        {
            return Err(errors);
        }

        Ok(AdvisorOutput {
            advisor_uuid: self.advisor_uuid,
            gbu_esid: self.gbu_esid.unwrap_or_default(),
            first_name,
            last_name,
            address1,
            address2,
            city,
            state,
            zip,
            email,
            phone,
            statelic,
        })
    }
}

//-------------------------------------------------------------------------------------------------
#[granite::gtype(RsType, RsPartial, RsError, RsDebug)]
pub struct Contact {
    pub first_name: String,
    pub last_name: String,
    pub email: String,
    pub phone: String,
    pub address1: String,
    pub address2: Option<String>,
    pub city: String,
    pub state: String,
    pub zip: String,
}
impl Contact_Partial {
    #[allow(clippy::result_large_err)]
    pub fn validate(&self) -> Result<Contact, Contact_Error> {
        let mut errors = Contact_Error {
            first_name: None,
            last_name: None,
            email: None,
            phone: None,
            address1: None,
            address2: None,
            city: None,
            state: None,
            zip: None,
        };

        let first_name = self
            .first_name
            .clone()
            .unwrap_or_default()
            .trim()
            .to_string();
        let last_name = self
            .last_name
            .clone()
            .unwrap_or_default()
            .trim()
            .to_string();
        let email = self.email.clone().unwrap_or_default().trim().to_string();
        let phone = self.phone.clone().unwrap_or_default().trim().to_string();
        let address1 = self.address1.clone().unwrap_or_default().trim().to_string();
        let mut address2 = self.address2.clone().unwrap_or_default();
        let city = self.city.clone().unwrap_or_default().trim().to_string();
        let state = self.state.clone().unwrap_or_default().trim().to_string();
        let zip = self.zip.clone().unwrap_or_default().trim().to_string();

        if first_name.is_empty() {
            errors.first_name = Some("First name is required.".to_string());
        }
        if last_name.is_empty() {
            errors.last_name = Some("Last name is required.".to_string());
        }
        if email.is_empty() {
            errors.email = Some("Email is required.".to_string());
        }
        if phone.is_empty() {
            errors.phone = Some("Phone is required.".to_string());
        }
        if address1.is_empty() {
            errors.address1 = Some("Address is required.".to_string());
        }
        if let Some(a) = address2 {
            if a.is_empty() {
                address2 = None;
            } else {
                address2 = Some(a.trim().to_string());
            }
        }
        if city.is_empty() {
            errors.city = Some("City is required.".to_string());
        }
        if state.is_empty() {
            errors.state = Some("State is required.".to_string());
        }
        if zip.is_empty() {
            errors.zip = Some("Zip is required.".to_string());
        }

        if errors.first_name.is_some()
            || errors.last_name.is_some()
            || errors.email.is_some()
            || errors.phone.is_some()
            || errors.address1.is_some()
            || errors.city.is_some()
            || errors.state.is_some()
            || errors.zip.is_some()
        {
            return Err(errors);
        }

        Ok(Contact {
            first_name: first_name.to_string(),
            last_name,
            email,
            phone,
            address1,
            address2,
            city,
            state,
            zip,
        })
    }
}

//-------------------------------------------------------------------------------------------------
#[granite::gtype(RsType, RsPartial, RsError, RsDebug)]
pub struct Crs {}
impl Crs {
    pub fn validate(self) -> Result<Crs, Crs_Error> {
        Err(Crs_Error {})
    }
}

//-------------------------------------------------------------------------------------------------
#[granite::gtype(ApiOutput)]
pub struct DebtOutput {
    pub debts: Vec<DebtRow>,
}

#[granite::gtype]
pub struct DebtInput {
    pub debts: Vec<DebtRowInput>,
}

#[granite::gtype(ApiOutput)]
pub struct DebtError {
    pub message: String,
    pub error_map: HashMap<Uuid, DebtRowError>,
}

#[granite::gtype(ApiOutput)]
pub struct DebtRow {
    pub client_debt_uuid: Uuid,
    pub is_external: bool,
    pub name: String,
    pub balance: Decimal,
    pub balance_date: DateUtc,
    pub interest: Decimal,
    pub payment: Decimal,
}

#[granite::gtype]
pub struct DebtRowInput {
    pub client_debt_uuid: Uuid,
    pub is_external: bool,
    pub name: Option<String>,
    pub balance: Option<Decimal>,
    pub balance_date: Option<DateUtc>,
    pub interest: Option<Decimal>,
    pub payment: Option<Decimal>,
    pub active: bool,
}

#[granite::gtype(ApiOutput)]
pub struct DebtRowError {
    pub client_debt_uuid: Uuid,
    pub outer: String,
    pub name: Option<String>,
    pub balance: Option<String>,
    pub balance_date: Option<String>,
    pub interest: Option<String>,
    pub payment: Option<String>,
}

impl DebtInput {
    pub fn validate(&self) -> Result<DebtOutput, DebtError> {
        if self.debts.is_empty() {
            return Err(DebtError {
                message: "Must have at least one debt.".to_string(),
                error_map: HashMap::new(),
            });
        }

        let mut debt_list = Vec::new();
        let mut error_map = HashMap::new();

        for debt in self.debts.iter().filter(|debt| debt.active) {
            let mut error = DebtRowError {
                client_debt_uuid: debt.client_debt_uuid,
                outer: String::new(),
                name: None,
                balance: None,
                balance_date: None,
                interest: None,
                payment: None,
            };

            match (
                &debt.name,
                debt.balance,
                debt.balance_date,
                debt.interest,
                debt.payment,
            ) {
                (Some(name), Some(balance), Some(balance_date), Some(interest), Some(payment)) => {
                    let name = name.trim().to_string();

                    if name.is_empty() {
                        error.name = Some("Name is required.".to_string());
                    }

                    if balance.lt(&Decimal::ZERO) {
                        error.balance = Some("Balance must be positive.".to_string());
                    }

                    if balance.gt(&Decimal::new(1000000000, 0)) {
                        error.balance =
                            Some("Balance must be less than $1,000,000,000.".to_string());
                    }

                    if interest.lt(&Decimal::ZERO) {
                        error.interest = Some("Interest must be positive.".to_string());
                    }

                    if interest.gt(&Decimal::new(50, 0)) {
                        error.interest = Some("Interest must be less than 50%.".to_string());
                    }

                    if payment.lt(&Decimal::ZERO) {
                        error.payment = Some("Payment must be positive.".to_string());
                    }

                    if payment.gt(&Decimal::new(1000000, 0)) {
                        error.payment = Some("Payment must be less than $1,000,000.".to_string());
                    }

                    if error.name.is_some()
                        || error.balance.is_some()
                        || error.balance_date.is_some()
                        || error.interest.is_some()
                        || error.payment.is_some()
                    {
                        error_map.insert(debt.client_debt_uuid, error);
                    } else {
                        debt_list.push(DebtRow {
                            client_debt_uuid: debt.client_debt_uuid,
                            is_external: debt.is_external,
                            name,
                            balance,
                            balance_date,
                            interest,
                            payment,
                        });
                    }
                }
                _ => {
                    if debt.name.is_none() {
                        error.name = Some("Name is required.".to_string());
                    }
                    if debt.balance.is_none() {
                        error.balance = Some("Balance is required.".to_string());
                    }
                    if debt.balance_date.is_none() {
                        error.balance_date = Some("Balance date is required.".to_string());
                    }
                    if debt.interest.is_none() {
                        error.interest = Some("Interest is required.".to_string());
                    }
                    if debt.payment.is_none() {
                        error.payment = Some("Payment is required.".to_string());
                    }
                    error_map.insert(debt.client_debt_uuid, error);
                }
            };
        }

        if !error_map.is_empty() {
            Err(DebtError {
                message:
                    "There are some errors in your input.  Please correct the errors and try again"
                        .to_string(),
                error_map,
            })
        } else {
            Ok(DebtOutput { debts: debt_list })
        }
    }
}

//-------------------------------------------------------------------------------------------------
#[granite::gtype(ApiOutput)]
pub struct BudgetOutput {
    pub budget_extra_debts: Decimal,
    pub budget_extra_savings: Decimal,
    pub budget_extra_retirement: Decimal,
    pub budget_extra_surplus: Decimal,
}

pub struct BudgetInput {
    pub budget_extra_debts: Option<Decimal>,
    pub budget_extra_savings: Option<Decimal>,
    pub budget_extra_retirement: Option<Decimal>,
    pub budget_extra_surplus: Option<Decimal>,
}

#[granite::gtype(ApiOutput)]
pub struct BudgetError {
    pub message: String,
    pub budget_extra_debts: Option<String>,
    pub budget_extra_savings: Option<String>,
    pub budget_extra_retirement: Option<String>,
    pub budget_extra_surplus: Option<String>,
}

impl BudgetInput {
    pub fn validate(&self) -> Result<BudgetOutput, BudgetError> {
        let mut error = BudgetError {
            message:
                "There are some errors in your input.  Please correct the errors and try again."
                    .to_string(),
            budget_extra_debts: None,
            budget_extra_savings: None,
            budget_extra_retirement: None,
            budget_extra_surplus: None,
        };

        match self.budget_extra_debts {
            Some(d) => {
                if d.lt(&Decimal::ZERO) {
                    error.budget_extra_debts = Some("Must be positive.".to_string());
                }
                if d.gt(&Decimal::new(1000000000, 0)) {
                    error.budget_extra_debts =
                        Some("Must be less than $1,000,000,000.".to_string());
                }
            }
            None => {
                error.budget_extra_debts = Some("Input is required (it can be $0).".to_string());
            }
        }

        match self.budget_extra_savings {
            Some(d) => {
                if d.lt(&Decimal::ZERO) {
                    error.budget_extra_savings = Some("Must be positive.".to_string());
                }
                if d.gt(&Decimal::new(1000000000, 0)) {
                    error.budget_extra_savings =
                        Some("Must be less than $1,000,000,000.".to_string());
                }
            }
            None => {
                error.budget_extra_savings = Some("Input is required (it can be $0).".to_string());
            }
        }

        match self.budget_extra_retirement {
            Some(d) => {
                if d.lt(&Decimal::ZERO) {
                    error.budget_extra_retirement = Some("Must be positive.".to_string());
                }
                if d.gt(&Decimal::new(1000000000, 0)) {
                    error.budget_extra_retirement =
                        Some("Must be less than $1,000,000,000.".to_string());
                }
            }
            None => {
                error.budget_extra_retirement =
                    Some("Input is required (it can be $0).".to_string());
            }
        }

        match self.budget_extra_surplus {
            Some(d) => {
                if d.lt(&Decimal::ZERO) {
                    error.budget_extra_surplus = Some("Must be positive.".to_string());
                }
                if d.gt(&Decimal::new(1000000000, 0)) {
                    error.budget_extra_surplus =
                        Some("Must be less than $1,000,000,000.".to_string());
                }
            }
            None => {
                error.budget_extra_surplus = Some("Input is required (it can be $0).".to_string());
            }
        }

        if error.budget_extra_debts.is_some()
            || error.budget_extra_savings.is_some()
            || error.budget_extra_retirement.is_some()
            || error.budget_extra_surplus.is_some()
        {
            return Err(error);
        }

        Ok(BudgetOutput {
            budget_extra_debts: self.budget_extra_debts.unwrap_or_default(),
            budget_extra_savings: self.budget_extra_savings.unwrap_or_default(),
            budget_extra_retirement: self.budget_extra_retirement.unwrap_or_default(),
            budget_extra_surplus: self.budget_extra_surplus.unwrap_or_default(),
        })
    }
}

pub struct ICoverOutput {
    pub icover_url: String,
}

pub struct ICoverError {
    pub messages: Vec<String>,
}
