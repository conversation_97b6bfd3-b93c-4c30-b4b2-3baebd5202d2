#[approck::http(GET /myaccount/{identity_uuid:Uuid}/advisor/{advisor_uuid:Uuid}/statelic; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use approck::html;

        doc.set_title("Edit State Licenses");

        use crate::api::myaccount::statelic::advisor_onboarding_checklist_statelic_get;

        let output = advisor_onboarding_checklist_statelic_get::call(
            app,
            identity,
            advisor_onboarding_checklist_statelic_get::Input {
                advisor_uuid: path.advisor_uuid,
            },
        )
        .await?;

        let panel = {
            let mut panel = bux::component::save_cancel_form_panel(
                "Edit State Licenses",
                &format!("/myaccount/{}/", path.identity_uuid),
            );
            panel.set_hidden("advisor_uuid", path.advisor_uuid);
            panel.add_body(html!(
                p.text-center { "Select the U.S. states for which you hold an official advisor license." }
                hr;
                div {
                    @for state in output.states {
                        (bux::input::checkbox::name_label_value_checked(
                            "state_code",
                            &state.label,
                            &state.state_code,
                            state.has_license
                        ))
                    }
                }
            ));
            panel
        };

        doc.add_body(html!(
            grid-12 {
                cell-2 {}
                cell-8 { (panel) }
                cell-2 {}
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}
