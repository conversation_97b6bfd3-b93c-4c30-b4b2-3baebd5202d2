//-------------------------------------------------------------------------------------------------
// 1. Import Components
import "./add.mcss";
import "@bux/input/text/string.mts";
import "@bux/input/textarea/string.mts";

//-------------------------------------------------------------------------------------------------
// 2. Import Code
import { SE } from "@granite/lib.mts";
import { client_add } from "@crate/api/client/addλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputTextString from "@bux/input/text/string.mts";
import BuxInputTextareaString from "@bux/input/textarea/string.mts";
import FormPanel from "@bux/component/form_panel.mts";

//-------------------------------------------------------------------------------------------------
// 3. Find Elements
const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $advisor_uuid: HTMLInputElement = SE($form, "[name=advisor_uuid]");
const $first_name: BuxInputTextString = SE($form, "[name=first_name]");
const $last_name: BuxInputTextString = SE($form, "[name=last_name]");
const $email: BuxInputTextString = SE($form, "[name=email]");
const $phone: BuxInputTextString = SE($form, "[name=phone]");
const $note: BuxInputTextareaString = SE($form, "[name=note]");

//-------------------------------------------------------------------------------------------------
// 4. Bind Event Handlers

//-------------------------------------------------------------------------------------------------
// 5. Write Code
new FormPanel({
    $form,
    api: client_add.api,
    on_cancel: go_back,

    err: (errors) => {
        $first_name.set_e(errors.first_name);
        $last_name.set_e(errors.last_name);
        $email.set_e(errors.email);
        $phone.set_e(errors.phone);
        $note.set_e(errors.note);
    },

    get: () => {
        return {
            advisor_uuid: $advisor_uuid.value,
            first_name: $first_name.value,
            last_name: $last_name.value,
            email: $email.value_option,
            phone: $phone.value_option,
            note: $note.value_option,
        };
    },

    set: (_value) => {
    },

    out: (output) => {
        go_next(output.onboarding_url);
    },
});
