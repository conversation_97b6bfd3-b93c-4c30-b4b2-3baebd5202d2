#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/add; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document, path: Path) -> Response {
        use maud::html;

        doc.set_title("Add a New Client");

        let mut form_panel = bux::component::add_cancel_form_panel(
            "Add a New Client",
            &crate::ml_advisor_client_list(path.advisor_uuid),
        );

        form_panel.set_id("client-add");
        form_panel.set_hidden("advisor_uuid", path.advisor_uuid);

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            grid-2 {
                div {
                    grid-2 {
                        (bux::input::text::string::name_label_value("first_name", "First Name:", None))
                        (bux::input::text::string::name_label_value("last_name", "Last Name:", None))
                        (bux::input::text::string::name_label_value("phone", "Mobile Phone:", None))
                        (bux::input::text::string::name_label_value("email", "Personal Email:", None))
                    }

                    (bux::input::textarea::string::name_label_value("note", "Note:", None))
        
                }
                div {
                    h2 { "About Adding a Client" }
                    p { "This is the first step to onboarding a new client on the Debt2Capital™ platform." }
                    p { "In the event you are working with a married client, this is the information for the " b { "primary applicant" } ".  On a subsequent page, you will be able to add relevant spousal information." }
                }
            }
        ));
        doc.add_body(html! {
            (form_panel)
        });
        Response::HTML(doc.into())
    }
}
