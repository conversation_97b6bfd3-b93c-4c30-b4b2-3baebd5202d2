#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/wizard/crs; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use approck::html;

        doc.set_title("Credit Report");

        use crate::api::client::wizard::client_wizard_context;

        let wizard_data = client_wizard_context::call(
            app,
            identity,
            client_wizard_context::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        let wizard = {
            let mut wizard = bux::component::form_wizard::new(WizardStep::Crs, wizard_data)?;
            wizard.set_hidden("client_uuid", path.client_uuid);
            wizard.add_heading("Pull Your Client's Credit Report");
            wizard.add_description("Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus massa massa, imperdiet eu nulla ac, porta ornare mi. Curabitur eget euismod mauris.");
            wizard.add_body(html!(
                div.df4l-advisor-onboarding-crs {
                    hr;
                    img."mw-100" src="https://asset7.net/jessicagarber/testing/Debt2Capital/crs-demo-screenshot.png" {}
                }
            ));
            wizard
        };

        doc.hide_page_nav();
        doc.add_body(html!(
            div.constrain-width-md {
                (wizard)
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}
