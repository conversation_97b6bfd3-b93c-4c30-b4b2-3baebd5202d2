#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/wizard/icover; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use approck::html;

        doc.set_title("ICover");

        use crate::api::client::wizard::client_wizard_context;
        use crate::api::client::wizard::icover::client_icover_get;

        let wizard_data = client_wizard_context::call(
            app,
            identity,
            client_wizard_context::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        let icover_output = client_icover_get::call(
            app,
            identity,
            client_icover_get::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        let wizard = {
            let mut wizard = bux::component::form_wizard::new(WizardStep::ICover, wizard_data)?;
            wizard.set_id("icover-wizard");
            wizard.set_hidden("client_uuid", path.client_uuid);
            wizard.add_heading("Start The Journey");
            wizard.add_description(
                "Automatically send client information to iCover to start the journey.",
            );
            wizard.add_body(html!(
                @match icover_output {
                    client_icover_get::Output::Ready { icover_url } => {
                        a #icover-link href=(icover_url) target="_blank" rel="noopener noreferrer" {
                            i."fas fa-external-link-alt" {}
                            " Take me to iCover"
                        }
                    }
                    client_icover_get::Output::NotReady { messages } => {
                        #errors {
                            p {
                                "You cannot proceed to iCover until the following errors are resolved:"
                            }
                            ul {
                                @for message in messages {
                                    li { (message) }
                                }
                            }
                        }
                    }
                }
            ));
            wizard
        };

        doc.add_body(html!(
            div.constrain-width-md {
                (wizard)
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}
