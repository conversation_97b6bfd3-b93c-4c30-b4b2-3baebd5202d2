//! # Client Onboarding Wizard - Introduction Page
//!
//! This module provides the introduction/landing page for the Debt2Capital™ client onboarding wizard.
//! The wizard guides financial advisors through a comprehensive 7-step process to onboard new clients
//! for debt elimination and wealth building strategies.
//!
//! ## Wizard Flow Overview
//!
//! The complete onboarding process consists of the following steps:
//!
//! 1. **Introduction** (this page) - Welcome and overview of the onboarding process
//! 2. **Client Info** - Collect basic client contact information and demographics
//! 3. **Credit Reports** - Pull and review client's credit report data
//! 4. **Review Debts** - Analyze and manage client's debt portfolio from credit reports and manual entries
//! 5. **Make Budget** - Set client's financial budget including extra payments and surplus
//! 6. **View Report** - Generate comprehensive Debt2Capital™ analysis and recommendations
//! 7. **Setup Policy** - Integration with iCover for whole life insurance policy setup
//!
//! ## Purpose
//!
//! This wizard enables financial advisors to:
//! - Systematically collect all necessary client financial information
//! - Pull and analyze credit report data for debt assessment
//! - Create personalized debt elimination strategies using the Debt2Capital™ methodology
//! - Generate professional reports showing potential savings and timelines
//! - Seamlessly transition clients to insurance policy setup through iCover integration
//!
//! The Debt2Capital™ approach uses whole life insurance policies with paid-up additions
//! to create a systematic debt elimination strategy that builds wealth while paying off debts.

#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/wizard/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use maud::html;

        use crate::api::client::wizard::client_wizard_context;

        let wizard_data = client_wizard_context::call(
            app,
            identity,
            client_wizard_context::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        let mut wizard = bux::component::form_wizard::new(WizardStep::Index, wizard_data)?;
        wizard.set_id("wizard-index");
        wizard.add_heading("Welcome to Debt2Capital™ Client Onboarding");
        wizard.add_description("Transform your client's financial future with our comprehensive debt elimination and wealth building strategy.");

        wizard.add_body(html!(
            div #wizard-intro-content {
                div #benefits-section {
                    div #benefits-grid {
                        div {
                            h4 { "🏦 Be Your Own Bank" }
                            p { "Personal banking system" }
                        }
                        div {
                            h4 { "💰 Debt Freedom" }
                            p { "Pay off debts faster" }
                        }
                        div {
                            h4 { "📈 Compound Growth" }
                            p { "Money keeps growing" }
                        }
                        div {
                            h4 { "🛡️ Financial Security" }
                            p { "Insurance + cash value" }
                        }
                    }
                }

                grid-2 {
                    div #intro-section {
                        h3 { "What is Debt2Capital™?" }
                        p {
                            "An innovative strategy using whole life insurance with paid-up additions "
                            "to eliminate debt while building wealth. Creates a personal banking system "
                            "that keeps your money working for you."
                        }
                    }

                    div #process-overview {
                        h3 { "Process Steps" }
                        ol #wizard-steps {
                            li { strong { "Client Info" } " - Contact details" }
                            li { strong { "Credit Report" } " - Pull credit profile" }
                            li { strong { "Review Debts" } " - Analyze obligations" }
                            li { strong { "Budget" } " - Set available funds" }
                            li { strong { "Report" } " - Generate analysis" }
                            li { strong { "Policy Setup" } " - Connect to iCover" }
                        }
                    }
                }
            }
        ));

        doc.hide_page_nav();
        doc.add_body(html!((wizard)));

        Ok(Response::HTML(doc.into()))
    }
}
