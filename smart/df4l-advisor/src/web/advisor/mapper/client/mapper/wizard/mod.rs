pub mod budget;
pub mod contact;
pub mod crs;
pub mod debt;
pub mod icover;
pub mod index;
pub mod report;
#[approck::prefix(/advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/wizard/)]
pub mod prefix {
    pub fn auth() {}
}

use bux::component::form_wizard::FormWizardImpl;

pub type WizardData = crate::api::client::wizard::client_wizard_context::Output;

#[derive(PartialEq)]
pub enum WizardStep {
    Index,
    Contact,
    Crs,
    Debt,
    Budget,
    Report,
    ICover,
}

#[allow(refining_impl_trait)]
impl FormWizardImpl for WizardStep {
    type Context = WizardData;
    fn all_variants() -> Vec<Self> {
        vec![
            WizardStep::Index,
            WizardStep::Contact,
            WizardStep::Crs,
            WizardStep::Debt,
            WizardStep::Budget,
            WizardStep::Report,
            WizardStep::ICover,
        ]
    }

    fn label(&self, _ctx: &WizardData) -> String {
        match self {
            WizardStep::Index => "Intro".to_string(),
            WizardStep::Contact => "Client Info".to_string(),
            WizardStep::Crs => "Credit Reports".to_string(),
            WizardStep::Debt => "Review Debts".to_string(),
            WizardStep::Budget => "Make Budget".to_string(),
            WizardStep::Report => "View Report".to_string(),
            WizardStep::ICover => "Setup Policy".to_string(),
        }
    }

    fn complete(&self, _step: &WizardStep, ctx: &WizardData) -> bool {
        match self {
            WizardStep::Index => true,
            WizardStep::Contact => ctx.contact_complete,
            WizardStep::Crs => ctx.crs_complete,
            WizardStep::Debt => ctx.debt_complete,
            WizardStep::Budget => ctx.budget_complete,
            WizardStep::Report => ctx.report_complete,
            WizardStep::ICover => ctx.icover_complete,
        }
    }

    fn enabled(&self, _step: &WizardStep, ctx: &Self::Context) -> bool {
        match self {
            WizardStep::Index => true,
            WizardStep::Contact => true,
            WizardStep::Crs => ctx.contact_complete,
            WizardStep::Debt => ctx.contact_complete,
            WizardStep::Budget => ctx.contact_complete,
            WizardStep::Report => ctx.contact_complete && ctx.debt_complete && ctx.budget_complete,
            WizardStep::ICover => ctx.contact_complete && ctx.debt_complete && ctx.budget_complete,
        }
    }

    fn href(&self, ctx: &WizardData) -> String {
        match self {
            WizardStep::Index => crate::ml_advisor_client_wizard(ctx.advisor_uuid, ctx.client_uuid),
            WizardStep::Contact => {
                crate::ml_advisor_client_wizard_contact(ctx.advisor_uuid, ctx.client_uuid)
            }
            WizardStep::Crs => {
                crate::ml_advisor_client_wizard_crs(ctx.advisor_uuid, ctx.client_uuid)
            }
            WizardStep::Debt => {
                crate::ml_advisor_client_wizard_debt(ctx.advisor_uuid, ctx.client_uuid)
            }
            WizardStep::Budget => {
                crate::ml_advisor_client_wizard_budget(ctx.advisor_uuid, ctx.client_uuid)
            }
            WizardStep::Report => {
                crate::ml_advisor_client_wizard_report(ctx.advisor_uuid, ctx.client_uuid)
            }
            WizardStep::ICover => {
                crate::ml_advisor_client_wizard_icover(ctx.advisor_uuid, ctx.client_uuid)
            }
        }
    }
}
