#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/wizard/budget; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use approck::html;

        doc.set_title("Budget Information");

        use crate::api::client::wizard::budget::client_budget_get;
        use crate::api::client::wizard::client_wizard_context;

        let wizard_context = client_wizard_context::call(
            app,
            identity,
            client_wizard_context::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        let output = client_budget_get::call(
            app,
            identity,
            client_budget_get::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        let wizard = {
            let mut wizard = bux::component::form_wizard::new(WizardStep::Budget, wizard_context)?;
            wizard.set_id("budget-entry");
            wizard.add_heading("Set Your Client's Budget");
            wizard.add_description(
                "Please provide information about your client's current financial situation.",
            );
            wizard.set_hidden("client_uuid", path.client_uuid);
            wizard.add_body(html!(
                grid-2 #splitter {
                    #question-list {
                        // Question 1: Extra debt payments
                        div {
                            div { "Are you making extra payments on any of your current debts, including your mortgage?" }
                            div {
                                (bux::input::text::currency::currency_input("budget_extra_debts", "How much are you paying extra per month?", output.budget_extra_debts))
                                small { "Enter $0 if you are not making extra payments." }
                            }
                        }

                        // Question 2: Retirement contributions
                        div {
                            div { "Are you putting money into a retirement plan that is NOT being matched?" }
                            div {
                                (bux::input::text::currency::currency_input("budget_extra_retirement", "How much are you contributing per month?", output.budget_extra_retirement))
                                small { "Enter $0 if you are not contributing to a retirement plan." }
                            }
                        }

                        // Question 3: Regular savings
                        div {
                            div { "Are you putting money into savings on a regular basis?" }
                            div {
                                (bux::input::text::currency::currency_input("budget_extra_savings", "How much are you saving per month?", output.budget_extra_savings))
                                small { "Enter $0 if you are not saving." }
                            }
                        }

                        // Question 4: Mortgage program affordability
                        div {
                            div { "If we can show you how to get your mortgage paid off faster could you afford to put extra money towards this program without it affecting your lifestyle?" }
                            div {
                                (bux::input::text::currency::currency_input("budget_extra_surplus", "How much could you afford per month?", output.budget_extra_surplus))
                                small { "Enter $0 if you are not able to afford extra money." }
                            }
                        }
                    }

                    // Total display (calculated client-side)
                    #total-container {
                        div { "Total: " span #total { "0.00" } " per Month" }
                        p { "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed scelerisque, sem et tempor imperdiet, turpis lectus posuere leo, ac suscipit mauris leo a sapien. Nunc viverra diam et magna cursus ultricies." }
                        p."mb-0" { "Phasellus hendrerit est a ipsum accumsan egestas. Aenean nec mauris id orci pulvinar feugiat quis et purus. Aenean pulvinar nisi eget tortor aliquam, id scelerisque lectus scelerisque. Nunc porta blandit urna nec condimentum. Aliquam eu mi ligula." }
                    }
                }
            ));
            wizard
        };

        doc.hide_page_nav();
        doc.add_body(html!(
            div.constrain-width-md {
                (wizard)
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}
