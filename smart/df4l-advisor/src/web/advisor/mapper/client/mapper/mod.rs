pub mod index;
pub mod messages;
pub mod wizard;

#[approck::prefix(/advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/)]
pub mod prefix {
    pub fn menu(app: App, menu: Menu, advisor_uuid: Uuid, client_uuid: Uuid) {
        menu.set_label_name_uri(
            "Client Details",
            app.uuid_to_label(client_uuid),
            &crate::ml_advisor_client_details(advisor_uuid, client_uuid),
        );

        menu.add_link_icon(
            "Setup Wizard",
            &crate::ml_advisor_client_wizard(advisor_uuid, client_uuid),
            approck::Icon::emoji_wizard(),
        );

        menu.add_link_icon(
            "Sent Messages",
            &crate::ml_advisor_client_messages(advisor_uuid, client_uuid),
            approck::Icon::emoji_email(),
        );
    }
    pub fn auth() {}
}
