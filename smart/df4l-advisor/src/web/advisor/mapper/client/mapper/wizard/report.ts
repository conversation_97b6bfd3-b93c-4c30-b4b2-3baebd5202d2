//-------------------------------------------------------------------------------------------------
// 1. Import Components

import "./report.mcss";
import "@bux/component/form_wizard.mts";

//-------------------------------------------------------------------------------------------------
// 2. Import Code

import { SE } from "@granite/lib.mts";

//-------------------------------------------------------------------------------------------------
// 3. Select Elements

const $form = SE(document, "#report") as HTMLFormElement;

// Entire functionality of page conditional on this element existing.
if ($form.querySelector("content")) {
    document.querySelectorAll<HTMLTableRowElement>("table tbody tr").forEach((tr) => {
        const cells: string[] = Array.from(
            tr.querySelectorAll<HTMLTableCellElement>("td"),
        ).map((td) => td.textContent?.trim().replace(/\u00A0/g, " ") || "");

        const firstCell = cells[0];

        const isSpecialRow = firstCell === "Totals:" ||
            firstCell === "100% Paid Off:";

        if (isSpecialRow) {
            tr.classList.add("highlight-special");
        }
    });
}
