//-------------------------------------------------------------------------------------------------
// 1. Import Components
import "./debt.mcss";
import "@bux/component/form_wizard.mts";
import "@bux/input/text/currency.mts";
import "@bux/input/text/percentage.mts";
import "@bux/input/text/string.mts";
import "@bux/input/checkbox.mts";
import "@bux/input/date.mts";

//-------------------------------------------------------------------------------------------------
// 2. Import Code
import { SEC, unwrap_or_undefined } from "@granite/lib.mts";

import { lapi_add, lapi_tbody } from "./debtλ.mts";
import {
    client_debt_delete,
    client_debt_save,
    client_debt_save_status,
    client_debt_validate_all,
} from "@crate/api/client/wizard/debtλ.mts";

import { DebtRowError } from "@crate/core/client/wizardλ.mts";

import BuxInputCheckbox from "@bux/input/checkbox.mts";
import BuxInputTextString from "@bux/input/text/string.mts";
import BuxInputCurrency from "@bux/input/text/currency.mts";
import BuxInputPercentage from "@bux/input/text/percentage.mts";
import BuxInputDate from "@bux/input/date.mjs";

//-------------------------------------------------------------------------------------------------
// 3. Find Elements

const $form = SEC(HTMLFormElement, document, "#debt-editor");

const SET_E_MAP = new Map<string, (error: DebtRowError) => void>();

//-------------------------------------------------------------------------------------------------
// Entire functionality of page conditional on this element existing.

if ($form.querySelector("content")) {
    const refresh = function () {
        SET_E_MAP.clear();

        lapi_tbody.api
            .call({
                client_uuid,
            })
            .then((response) => {
                if ("Output" in response) {
                    $tbody.innerHTML = response.Output[0].tbody_inner_html;

                    for (
                        const tr of Array.from($tbody.querySelectorAll<HTMLTableRowElement>("tr"))
                    ) {
                        wrap_tr(tr);
                    }
                } else {
                    console.error(response);
                }
            })
            .catch((error) => {
                console.error(error);
            });
    };

    const wrap_tr = function (tr: string | HTMLTableRowElement): HTMLTableRowElement {
        let $tr: HTMLTableRowElement;
        if (typeof tr === "string") {
            const $tbody = document.createElement("tbody");
            $tbody.innerHTML = tr;
            $tr = $tbody.querySelector("tr") as HTMLTableRowElement;
        } else {
            $tr = tr;
        }

        const client_debt_uuid = $tr.getAttribute("client_debt_uuid") || "";
        const editable = $tr.hasAttribute("editable");
        const $active = SEC(BuxInputCheckbox, $tr, "bux-input-checkbox[name=active]");

        $active.on_change = save_status;

        function save_status() {
            client_debt_save_status
                .call({
                    client_uuid,
                    client_debt_uuid,
                    active: $active.value ?? false,
                })
                .then((response) => {
                    if ("Output" in response) {
                        // Status saved successfully
                    } else {
                        console.error(response);
                    }
                })
                .catch((error) => {
                    console.error(error);
                });
        }

        if (editable) {
            const $name = SEC(BuxInputTextString, $tr, "bux-input-text-string[name=name]");
            const $balance = SEC(BuxInputCurrency, $tr, "bux-input-text-currency[name=balance]");
            const $balance_date = SEC(BuxInputDate, $tr, "bux-input-date[name=balance_date]");
            const $interest_rate = SEC(BuxInputPercentage, $tr, "[name=interest_rate]");

            const $payment = SEC(BuxInputCurrency, $tr, "bux-input-text-currency[name=payment]");
            const $delete_button = SEC(HTMLButtonElement, $tr, "button.x-delete");

            const save = () => {
                client_debt_save
                    .call({
                        client_uuid,
                        client_debt_uuid,
                        name: $name.value_option,
                        balance: $balance.value_option,
                        balance_date: $balance_date.value_option,
                        interest: $interest_rate.value_option,
                        payment: $payment.value_option,
                    })
                    .then((response) => {
                        if ("Output" in response) {
                            // Saved successfully
                        } else {
                            console.error(response);
                        }
                    })
                    .catch((error) => {
                        console.error(error);
                    });
            };

            const delete_debt = () => {
                client_debt_delete
                    .call({
                        client_uuid,
                        client_debt_uuid,
                    })
                    .then((response) => {
                        if ("Output" in response) {
                            // Successfully deleted, refresh the table
                            refresh();
                        } else {
                            console.error(response);
                        }
                    })
                    .catch((error) => {
                        console.error(error);
                    });
            };

            $name.on_change = save;
            $balance.on_change = save;
            $balance_date.on_change = save;
            $interest_rate.on_change = save;
            $payment.on_change = save;
            $delete_button.addEventListener("click", (event) => {
                event.preventDefault();
                delete_debt();
            });

            SET_E_MAP.set(client_debt_uuid, (error: DebtRowError) => {
                $name.set_e(unwrap_or_undefined(error.name));
                $balance.set_e(unwrap_or_undefined(error.balance));
                $balance_date.set_e(unwrap_or_undefined(error.balance_date));
                $interest_rate.set_e(unwrap_or_undefined(error.interest));
                $payment.set_e(unwrap_or_undefined(error.payment));
            });
        }

        return $tr;
    };

    /// call the new_row api to get the new row html, then inject it into the tbody
    const add_debt = function () {
        lapi_add
            .call({
                client_uuid,
            })
            .then((response) => {
                if ("Output" in response) {
                    const $tr = wrap_tr(response.Output[0].tr_html);
                    $tbody.appendChild($tr);
                    $tr.querySelector<HTMLInputElement>("input[name=name]")?.focus();
                } else {
                    console.error(response);
                }
            })
            .catch((error) => {
                console.error(error);
            });
    };

    // -------------------------------------------------------------------------------------------------
    // 3. Find elements
    const $table = SEC(HTMLTableElement, $form, "table");
    const $tbody = SEC(HTMLTableSectionElement, $table, "tbody");
    const client_uuid = (SEC(HTMLInputElement, $form, "[name=client_uuid]")).value;

    const $x_add_link = SEC(HTMLAnchorElement, $form, ".x-add-debt");

    const $next_button = SEC(HTMLAnchorElement, $form, "a.x-next-button");
    const $error = SEC(HTMLElement, $form, "error");

    //-------------------------------------------------------------------------------------------------
    // 4. Bind event handlers
    $x_add_link.addEventListener("click", (event) => {
        event.preventDefault();
        add_debt();
    });

    // iterate over all the tr tags in tbody and wrap them
    for (const tr of Array.from($tbody.querySelectorAll<HTMLTableRowElement>("tr"))) {
        wrap_tr(tr);
    }

    // validate before continue
    $next_button.addEventListener("click", (event) => {
        event.preventDefault();
        $error.textContent = "";

        client_debt_validate_all
            .call({
                client_uuid,
            })
            .then((response) => {
                if ("Output" in response) {
                    const output = response.Output[0];
                    if ("Valid" in output) {
                        window.location.href = $next_button.href;
                    } else {
                        const invalid = output.Invalid;
                        $error.textContent = invalid.message;
                        for (const [client_debt_uuid, error] of invalid.error_map) {
                            const set_e = SET_E_MAP.get(client_debt_uuid);
                            if (set_e) {
                                set_e(error);
                            }
                        }
                        $form.reportValidity();
                    }
                } else {
                    console.error(response);
                }
            })
            .catch((error) => {
                console.error(error);
            });
    });

    //-------------------------------------------------------------------------------------------------
    // 5. All the other funcitonality
}
