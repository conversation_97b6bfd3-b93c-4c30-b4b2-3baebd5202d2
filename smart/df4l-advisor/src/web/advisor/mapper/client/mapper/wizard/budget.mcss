@import "./mod.mcss";

/* Minimal styling - let bux framework handle component styling */
/* Use semantic nested selectors instead of utility classes */


#budget-entry {

    #splitter {
        gap: 3rem;
    }

    #question-list {
        gap: 3rem;


        > div {
            margin-bottom: 1rem;

            > div:first-child {
                background-color: #f5f5dc;
                padding: 1rem;
                border-radius: 1rem;
                margin-bottom: 1rem;
                font-weight: 500;
                color: #222222;
            }

            > div:nth-child(2) {
                padding-left: 3rem;
            }

        }
    }

    #total-container {
        > div {
            padding: 1rem;
            border-radius: 1rem;
            background-color: #c0ffce;
            margin-bottom: 3rem;
            font-size: 2rem;
        }
    }

    #total {
        font-weight: bold;
        display: inline-block;
        margin: 0 0.5rem;
    }
}
