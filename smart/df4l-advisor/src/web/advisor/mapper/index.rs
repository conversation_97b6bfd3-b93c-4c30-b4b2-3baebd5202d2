#[approck::http(GET /advisor/{advisor_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::api::advisor::dashboard::dashboard;
        use crate::api::advisor::setup::advisor_setup_checklist;
        use approck::html;

        let advisor_dashboard = dashboard::call(
            app,
            identity,
            dashboard::Input {
                advisor_uuid: path.advisor_uuid,
            },
        )
        .await?;

        let checklist = advisor_setup_checklist::call(
            app,
            identity,
            advisor_setup_checklist::Input {
                advisor_uuid: path.advisor_uuid,
            },
        )
        .await?;

        doc.set_title("Advisor Details");

        let quick_info_box = html! {
            bux-ui-quick-info-box {
                x-item {
                    span.icon-text {
                        span.icon { "🧑‍💼" }
                        " "
                        a href=(crate::ml_advisor_client_list(path.advisor_uuid)) { "Active Clients" }
                        " "
                        a class="btn btn-primary" href=(crate::ml_advisor_client_add(path.advisor_uuid)) { "Add Client" }
                    }
                    h1 { (advisor_dashboard.active_clients) }
                }
                x-item {
                    span.icon-text {
                        span.icon { "🧑‍💼" }
                        " "
                        a href=(crate::ml_advisor_client0_list(path.advisor_uuid)) { "Active Legacy Clients" }
                    }
                    h1 { (advisor_dashboard.active_legacy_clients) }
                }
            }
        };

        // Only show setup section if there are incomplete items
        let setup_section = if !checklist.setup_items.is_empty() {
            html! {
                div.setup-incomplete {
                    h2.section-title { "Finish Your Setup" }
                    p.setup-description { "Complete these remaining items to fully activate your advisor account." }

                    div.setup-checklist {
                        @for item in &checklist.setup_items {
                            div.setup-item {
                                div.setup-item-content {
                                    h4.setup-item-title { (item.name) }
                                    p.setup-item-status { "Incomplete" }
                                }
                                a.setup-item-action href=(item.action_url) {
                                    "Continue Setup"
                                }
                            }
                        }
                    }
                }
            }
        } else {
            html! {
                div.setup-complete {
                    div.success-card {
                        div.success-icon { "✅" }
                        h2.success-title { "Setup Complete!" }
                        p.success-message { "All required setup items have been completed. Your advisor account is fully activated." }
                    }
                }
            }
        };

        doc.add_body(html!(
            main.advisor-dashboard.bux-narrow-75 {
                header.dashboard-header {
                    h1.welcome-title { "Welcome, " (advisor_dashboard.name) "!" }
                    p.welcome-subtitle { "Manage your advisor dashboard and client relationships" }
                }

                section.dashboard-overview {
                    (quick_info_box)
                }

                section.dashboard-content {
                    article.setup-section {
                        (setup_section)
                    }

                    article.profile-section {
                        div.profile-cards {
                            div.profile-card {
                                header.card-header {
                                    h3.card-title { "GBU Agent ID" }
                                    a.edit-link href=(crate::ml_myaccount_advisor_gbu(advisor_dashboard.identity_uuid, path.advisor_uuid)) {
                                        "Edit"
                                    }
                                }
                                div.card-content {
                                    @if let Some(gbu_id) = &advisor_dashboard.gbu_agent_id {
                                        span.profile-value { (gbu_id) }
                                    } @else {
                                        span.profile-value.not-set { "Not Set" }
                                    }
                                }
                            }

                            div.profile-card {
                                header.card-header {
                                    h3.card-title { "Licensed States" }
                                    @if !advisor_dashboard.statelic.is_empty() {
                                        a.edit-link href=(crate::ml_myaccount_advisor_statelic(advisor_dashboard.identity_uuid, path.advisor_uuid)) {
                                            "Edit"
                                        }
                                    }
                                }
                                div.card-content {
                                    @if advisor_dashboard.statelic.is_empty() {
                                        span.profile-value.not-set { "Not Set" }
                                    } @else {
                                        span.profile-value { (advisor_dashboard.statelic.join(", ")) }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}
