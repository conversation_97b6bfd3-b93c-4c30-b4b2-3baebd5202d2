#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client0/{client_uuid:Uuid}/edit; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Edit Client: Adrian Gallinal");

        let mut panel = bux::component::save_cancel_form_panel(
            "Edit Client: Adrian <PERSON>",
            "/advisor/00000000-0000-0000-0000-000000000000/client0/00000000-0000-0000-0000-000000000000/",
        );

        #[rustfmt::skip]
        panel.add_body(maud::html!(
            (bux::input::text::string::name_label_readonly("advisor_name", "Advisor Name:", "A. <PERSON>"))
            (bux::input::text::string::name_label_readonly("client_name", "Client Name:", "Adrian Gallinal"))
            (bux::input::text::string::name_label_value("email", "Email:", None))
            (bux::input::text::string::name_label_value("text_message", "Send Text Messages To Phone Number:", None))
            (bux::input::checkbox::name_label_checked("client_active", "Client Active", false))
        ));
        doc.add_body(html! {
            bux-action-panel {
                (panel)
            }
        });
        Response::HTML(doc.into())
    }
}
