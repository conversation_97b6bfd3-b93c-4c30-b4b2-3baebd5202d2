#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client0/{client_uuid:Uuid}/debt/add; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        _app: App,
        _identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use approck::html;

        doc.set_title("Add New Debt Account");

        let mut panel = bux::component::add_cancel_form_panel(
            "Add New Debt Account",
            &crate::ml_advisor_client0_debt_list(path.advisor_uuid, path.client_uuid),
        );

        #[rustfmt::skip]
        panel.add_body(maud::html!(
            input type="hidden" name="client_uuid" value=(path.client_uuid.to_string()) {}
            input type="hidden" name="advisor_uuid" value=(path.advisor_uuid.to_string()) {}

            (bux::input::text::string::name_label_value("name", "Debt:", None))
            grid-3 {
                (bux::input::text::currency::currency_input("balance", "Current Balance: ", None))
                (bux::input::text::percentage::percentage_input("interest_rate", "Interest Rate:", None))
                (bux::input::text::currency::currency_input("monthly_payment", "Monthly Payment: ", None))
            }
            (bux::input::textarea::string::name_label_value("note", "Note:", None))
        ));
        doc.add_body(html! {
            section {
                (panel)
            }
        });

        Ok(Response::HTML(doc.into()))
    }
}
