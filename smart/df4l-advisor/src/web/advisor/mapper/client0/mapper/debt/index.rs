#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client0/{client_uuid:Uuid}/debt/?keyword=Option<String>&active=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
        qs: QueryString,
    ) -> Result<Response> {
        use bux::format_currency_us_0;
        use bux::format_percentage_us_2;
        use maud::html;

        doc.page_nav_add_record(
            "Add New Debt Account",
            &crate::ml_advisor_client0_debt_add(path.advisor_uuid, path.client_uuid),
        );
        doc.set_title("List of Debts");
        doc.set_body_display_fluid();

        use crate::api::client0::debt::list::advisor_client_debt_list;

        let active = bux::parse_active_qs(&qs.active, Some(true));
        let output = advisor_client_debt_list::call(
            app,
            identity,
            advisor_client_debt_list::Input {
                client_uuid: path.client_uuid,
                keyword: qs.keyword.clone(),
                active,
            },
        )
        .await?;

        let mut dt = bux::component::detail_table(output.debt_list);

        dt.add_keyword_filter(qs.keyword.as_deref());
        dt.add_active_filter(active);

        dt.add_column("Debt", |a| html! { (a.name) });
        dt.add_column("Balance", |a| {
            html! {
                (a.balance.map(format_currency_us_0).unwrap_or_else(|| "N/A".to_string()))
            }
        });
        dt.add_column("Interest Rate", |a| {
            html! {
                (a.interest_rate.map(format_percentage_us_2).unwrap_or_else(|| "N/A".to_string()))
            }
        });
        dt.add_column("Payment", |a| {
            html! {
                (a.monthly_payment.map(format_currency_us_0).unwrap_or_else(|| "N/A".to_string()))
            }
        });
        dt.add_active_status_column("Client Status", |a| a.active);
        dt.add_details_column(|a| {
            crate::ml_advisor_client0_debt_details(
                a.advisor_uuid.unwrap(),
                a.client_uuid,
                a.client_debt_uuid,
            )
        });

        doc.add_body(html!((dt)));

        Ok(Response::HTML(doc.into()))
    }
}
