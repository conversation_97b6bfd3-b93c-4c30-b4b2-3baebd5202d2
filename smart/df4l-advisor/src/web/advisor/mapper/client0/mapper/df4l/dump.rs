#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client0/{client_uuid:Uuid}/df4l/dump?keyword=Option<String>; AUTH None; return HTML;)]
pub mod page {
    use bux::component::multi_header_table::{HeaderCell, multi_header_table};

    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use approck::html;
        use bux::format_currency_us_0;
        use df4l_zero::api::client_debt_free_info::client_debt_free_info;
        use granite::Decimal;
        use std::borrow::Cow;
        use std::collections::HashMap;

        fn format_currency_us_0_or_dash(value: Decimal) -> String {
            if value != Decimal::ZERO {
                format_currency_us_0(value)
            } else {
                "-".to_string()
            }
        }

        doc.set_title("View Debt Plan");
        doc.set_body_display_fixed();

        // Use df4l_zero API to return all the data, excluding plan
        let client_debt_info = client_debt_free_info::call(
            app,
            identity,
            client_debt_free_info::Input {
                advisor_uuid: path.advisor_uuid,
                client_uuid: path.client_uuid,
                generate_plan: true,
            },
        )
        .await?;

        //let data = df4l_zero::dump::list();

        pub struct Dump {
            pub month_index: String,
            pub date: String,
            pub total_budget: String,
            pub paid_insurance_premium: String,
            pub paid_minimum_payments: String,
            pub paid_extra_payments: String,
            pub paid_repay_insurance_loan: String,
            pub remaining_budget: String,
            pub total_premium: String,
            pub base_portion: String,
            pub pua_portion: String,
            pub added_to_pua: String,
            pub extra_added_to_pua: String,
            pub pua_balance: String,
            pub loan_starting_balance: String,
            pub loan_draws: String,
            pub loan_payments: String,
            pub loan_ending_balance: String,
            pub debt_starting_balance: String,
            pub debt_interest: String,
            pub debt_payments: String,
            pub debt_ending_balance: String,
            pub policy_cash_value: String,
            pub net_cash_and_debt: String,
            pub message: String,
            pub debt_hashmap: HashMap<String, Debt>,
        }

        pub struct Debt {
            pub starting_balance: String,
            pub interest: String,
            pub min_payment: String,
            pub extra_payment: String,
            pub payoff_from_loan: String,
            pub ending_balance: String,
        }

        // Build data vec of Dump {} using client_debt_info data
        let mut data = Vec::new();
        for month_plan in client_debt_info.debt_info.plan {
            let dump = Dump {
                month_index: month_plan.month.to_string(),
                date: month_plan.month_text.clone(),
                total_budget: format_currency_us_0_or_dash(month_plan.budget_total),
                paid_insurance_premium: format_currency_us_0_or_dash(month_plan.premium_total),
                paid_minimum_payments: format_currency_us_0_or_dash(
                    month_plan.budget_minimum_payments,
                ),
                paid_extra_payments: format_currency_us_0_or_dash(month_plan.budget_extra_payments),
                paid_repay_insurance_loan: format_currency_us_0_or_dash(
                    month_plan.budget_repayment,
                ),
                remaining_budget: format_currency_us_0_or_dash(month_plan.budget_remaining),
                total_premium: format_currency_us_0_or_dash(month_plan.premium_total),
                base_portion: format_currency_us_0_or_dash(month_plan.premium_base),
                pua_portion: format_currency_us_0_or_dash(month_plan.premium_pua),
                added_to_pua: format_currency_us_0_or_dash(month_plan.pua_added),
                extra_added_to_pua: format_currency_us_0_or_dash(month_plan.pua_extra),
                pua_balance: format_currency_us_0_or_dash(month_plan.pua_balance),
                loan_starting_balance: format_currency_us_0_or_dash(
                    month_plan.loan_starting_balance,
                ),
                loan_draws: format_currency_us_0_or_dash(month_plan.loan_draw),
                loan_payments: format_currency_us_0_or_dash(month_plan.loan_paid),
                loan_ending_balance: format_currency_us_0_or_dash(month_plan.loan_ending_balance),
                debt_starting_balance: format_currency_us_0_or_dash(
                    month_plan.debt_starting_balance,
                ),
                debt_interest: format_currency_us_0_or_dash(month_plan.debt_interest_charge),
                debt_payments: format_currency_us_0_or_dash(month_plan.debt_paid_down),
                debt_ending_balance: format_currency_us_0_or_dash(month_plan.debt_ending_balance),
                policy_cash_value: format_currency_us_0_or_dash(month_plan.cash_value),
                net_cash_and_debt: format_currency_us_0_or_dash(month_plan.cash_net),
                //message: month_plan.month_text
                //use SMSMap to compose message
                message: month_plan
                    .sms_map
                    .values()
                    .map(|v| v.to_string())
                    .collect::<Vec<String>>()
                    .join("\n"),
                debt_hashmap: month_plan
                    .debt_list
                    .iter()
                    .map(|d| {
                        (
                            (d.full_name.clone()),
                            Debt {
                                starting_balance: format_currency_us_0_or_dash(d.starting_balance),
                                interest: format_currency_us_0_or_dash(d.interest_charge),
                                min_payment: format_currency_us_0_or_dash(d.minimum_payment),
                                extra_payment: format_currency_us_0_or_dash(d.extra_payment),
                                payoff_from_loan: format_currency_us_0_or_dash(d.payoff_payment),
                                ending_balance: format_currency_us_0_or_dash(d.ending_balance),
                            },
                        )
                    })
                    .collect(),
            };
            data.push(dump);
        }

        let mut mht = multi_header_table(data);

        let mut h1row = vec![
            HeaderCell::new("Month", 2),
            HeaderCell::new("Budget", 6),
            HeaderCell::new("Insurance Premium", 3),
            HeaderCell::new("PUA Balance", 3),
            HeaderCell::new("Insurance Policy Loan", 4),
            HeaderCell::new("Month", 2),
            HeaderCell::new("Debt Balance", 4),
            HeaderCell::new("Value", 2),
        ];

        let mut h2row = vec![
            HeaderCell::new("#", 1),
            HeaderCell::new("Date", 1),
            HeaderCell::new("Total Budget", 1),
            HeaderCell::new("Paid To Insurance Premium", 1),
            HeaderCell::new("Paid To Minimum Payments", 1),
            HeaderCell::new("Paid To Extra Payments", 1),
            HeaderCell::new("Paid To Repay Insurance Loan", 1),
            HeaderCell::new("Remaining Budget", 1),
            HeaderCell::new("Total Premium", 1),
            HeaderCell::new("Base Portion", 1),
            HeaderCell::new("PUA Portion", 1),
            HeaderCell::new("Added to PUA", 1),
            HeaderCell::new("Extra Added to PUA", 1),
            HeaderCell::new("PUA Balance", 1),
            HeaderCell::new("Starting Loan Balance", 1),
            HeaderCell::new("Plus Draws From Loan", 1),
            HeaderCell::new("Minus Payments To Loan", 1),
            HeaderCell::new("Ending Loan Balance", 1),
            HeaderCell::new("#", 1),
            HeaderCell::new("Date", 1),
            HeaderCell::new("Starting Balance", 1),
            HeaderCell::new("Plus Interest Charges", 1),
            HeaderCell::new("Minus Payments", 1),
            HeaderCell::new("Ending Balance", 1),
            HeaderCell::new("Policy Cash Value", 1),
            HeaderCell::new("Net Cash And Debt", 1),
        ];

        for d in &client_debt_info.debt_info.debt_list {
            h1row.push(HeaderCell::new("Month", 2));
            //h1row.push(HeaderCell::new(&d.full_name.clone(), 6));
            // Use LSW  at 5.00%
            //h1row.push(HeaderCell::new(&d.full_name, 6));
            h1row.push(HeaderCell::new(
                format!("{} at {}%", d.full_name, d.interest_rate),
                6,
            ));

            h2row.push(HeaderCell::new("#", 1));
            h2row.push(HeaderCell::new("Date", 1));
            h2row.push(HeaderCell::new("Starting Balance", 1));
            h2row.push(HeaderCell::new("Interest", 1));
            h2row.push(HeaderCell::new("Min Payment", 1));
            h2row.push(HeaderCell::new("Extra Payment", 1));
            h2row.push(HeaderCell::new("Payoff From Loan", 1));
            h2row.push(HeaderCell::new("Ending Balance", 1));
        }
        // Append month and messages
        h1row.push(HeaderCell::new("Month", 2));
        h1row.push(HeaderCell::new("Messages", 1));

        h2row.push(HeaderCell::new("#", 1));
        h2row.push(HeaderCell::new("Date", 1));
        h2row.push(HeaderCell::new("Text Messages", 1));

        mht.add_header_row(h1row);
        mht.add_header_row(h2row);

        mht.add_header_row(vec![HeaderCell::new("", 45)]);

        // Columns
        mht.add_column_with_class(|a: &Dump| html! { (a.month_index) }, "cp-month");
        mht.add_column_with_class(|a: &Dump| html! { (a.date) }, "cp-month");
        mht.add_column_with_class(|a: &Dump| html! { (a.total_budget) }, "cp-budget");
        mht.add_column_with_class(|a: &Dump| html! { (a.paid_insurance_premium) }, "cp-budget");
        mht.add_column_with_class(|a: &Dump| html! { (a.paid_minimum_payments) }, "cp-budget");
        mht.add_column_with_class(|a: &Dump| html! { (a.paid_extra_payments) }, "cp-budget");
        mht.add_column_with_class(
            |a: &Dump| html! { (a.paid_repay_insurance_loan) },
            "cp-budget",
        );
        mht.add_column_with_class(|a: &Dump| html! { (a.remaining_budget) }, "cp-budget");
        mht.add_column_with_class(|a: &Dump| html! { (a.total_premium) }, "cp-insurance");
        mht.add_column_with_class(|a: &Dump| html! { (a.base_portion) }, "cp-insurance");
        mht.add_column_with_class(|a: &Dump| html! { (a.pua_portion) }, "cp-insurance");
        mht.add_column_with_class(|a: &Dump| html! { (a.added_to_pua) }, "cp-pua");
        mht.add_column_with_class(|a: &Dump| html! { (a.extra_added_to_pua) }, "cp-pua");
        mht.add_column_with_class(|a: &Dump| html! { (a.pua_balance) }, "cp-pua");
        mht.add_column_with_class(|a: &Dump| html! { (a.loan_starting_balance) }, "cp-loan");
        mht.add_column_with_class(|a: &Dump| html! { (a.loan_draws) }, "cp-loan");
        mht.add_column_with_class(|a: &Dump| html! { (a.loan_payments) }, "cp-loan");
        mht.add_column_with_class(|a: &Dump| html! { (a.loan_ending_balance) }, "cp-loan");
        mht.add_column_with_class(|a: &Dump| html! { (a.month_index) }, "cp-month");
        mht.add_column_with_class(|a: &Dump| html! { (a.date) }, "cp-month");
        mht.add_column_with_class(|a: &Dump| html! { (a.debt_starting_balance) }, "cp-debt");
        mht.add_column_with_class(|a: &Dump| html! { (a.debt_interest) }, "cp-debt");
        mht.add_column_with_class(|a: &Dump| html! { (a.debt_payments) }, "cp-debt");
        mht.add_column_with_class(|a: &Dump| html! { (a.debt_ending_balance) }, "cp-debt");
        mht.add_column_with_class(|a: &Dump| html! { (a.policy_cash_value) }, "cp-cash");
        mht.add_column_with_class(|a: &Dump| html! { (a.net_cash_and_debt) }, "cp-cash");

        for (i, d) in client_debt_info.debt_info.debt_list.iter().enumerate() {
            mht.add_column_with_class(|a: &Dump| html! { (a.month_index) }, "cp-month");
            mht.add_column_with_class(|a: &Dump| html! { (a.date) }, "cp-month");

            // Use Cow to avoid cloning
            let debt_id: Cow<'_, str> = Cow::Owned(d.full_name.clone());
            let debt_id_class = format!("cp-debt-{}", i);

            mht.add_column_with_class(move |a: &Dump| html! { (a.debt_hashmap.get(debt_id.as_ref()).unwrap().starting_balance) }, debt_id_class.to_string());
            let debt_id2: Cow<'_, str> = Cow::Owned(d.full_name.clone());
            mht.add_column_with_class(
                move |a: &Dump| html! { (a.debt_hashmap.get(debt_id2.as_ref()).unwrap().interest) },
                debt_id_class.to_string(),
            );
            let debt_id3: Cow<'_, str> = Cow::Owned(d.full_name.clone());
            mht.add_column_with_class(move |a: &Dump| html! { (a.debt_hashmap.get(debt_id3.as_ref()).unwrap().min_payment) }, debt_id_class.to_string());
            let debt_id4: Cow<'_, str> = Cow::Owned(d.full_name.clone());
            mht.add_column_with_class(move |a: &Dump| html! { (a.debt_hashmap.get(debt_id4.as_ref()).unwrap().extra_payment) }, debt_id_class.to_string());
            let debt_id5: Cow<'_, str> = Cow::Owned(d.full_name.clone());
            mht.add_column_with_class(move |a: &Dump| html! { (a.debt_hashmap.get(debt_id5.as_ref()).unwrap().payoff_from_loan) }, debt_id_class.to_string());
            let debt_id6: Cow<'_, str> = Cow::Owned(d.full_name.clone());
            mht.add_column_with_class(move |a: &Dump| html! { (a.debt_hashmap.get(debt_id6.as_ref()).unwrap().ending_balance) }, debt_id_class.to_string());

            //mht.add_column_with_class(|a: &Dump| html! { (a.bdo_starting_balance) }, "cp-debt-0");
        }
        // add month and message
        mht.add_column_with_class(|a: &Dump| html! { (a.month_index) }, "cp-month");
        mht.add_column_with_class(|a: &Dump| html! { (a.date) }, "cp-month");
        mht.add_column_with_class(|a: &Dump| html! { (a.message) }, "cp-message");

        doc.add_body(html!((mht)));

        Ok(Response::HTML(doc.into()))
    }
}
