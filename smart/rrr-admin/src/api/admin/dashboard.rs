#[approck::api]
pub mod admin_dashboard {

    #[granite::gtype(ApiInput)]
    pub struct Input {}

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub agent_count: i64,
    }

    pub async fn call(app: App) -> Result<Output> {
        // Auth check is handled automatically by the api macro

        let dbcx = app.postgres_dbcx().await?;

        let row = granite::pg_row!(
            db = dbcx;
            row = {
                agent_count: i64,
            };
            SELECT
                (SELECT COUNT(*) FROM rrr.agent WHERE active = true) AS agent_count
        )
        .await?;

        Ok(Output {
            agent_count: row.agent_count,
        })
    }
}
