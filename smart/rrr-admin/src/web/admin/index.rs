#[approck::http(GET /admin/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, doc: Document) -> Result<Response> {
        use maud::html;

        doc.set_title("RRR Admin Dashboard");

        use crate::api::admin::dashboard::admin_dashboard;
        let dashboard_data = admin_dashboard::call(app).await?;

        // Get user name for welcome message
        let user_name = "Admin".to_string();

        doc.add_body(html!(
            rrr-admin-dashboard {
                panel {
                    content {
                        header {
                            h1 { "Welcome, " (user_name) "!" }
                            p { "Here's a quick overview of your RRR system metrics." }
                        }
                        grid-2 {
                            admin-metric {
                                i.fas.fa-robot aria-hidden="true" {}
                                dl {
                                    dt {
                                        a href="/admin/agent/" { "Active Agents" }
                                    }
                                    dd { "(" (dashboard_data.agent_count) ")" }
                                }
                                a href="/admin/agent/" aria-label="View active agents" {
                                    i.fas.fa-chevron-right.fa-lg aria-hidden="true" {}
                                }
                            }
                            admin-metric {
                                i.fas.fa-users  aria-hidden="true" {}
                                dl {
                                    dt {
                                        a href="/admin/user/" { "Active Users" }
                                    }
                                    dd { "(0)" }
                                }
                                a href="/admin/user/" aria-label="View active users" {
                                    i.fas.fa-chevron-right.fa-lg aria-hidden="true" {}
                                }
                            }
                            admin-metric {
                                i.fas.fa-cog  aria-hidden="true" {}
                                dl {
                                    dt {
                                        a href="/admin/settings/" { "System Settings" }
                                    }
                                    dd { "Configure" }
                                }
                                a href="/admin/settings/" aria-label="View system settings" {
                                    i.fas.fa-chevron-right.fa-lg aria-hidden="true" {}
                                }
                            }
                            admin-metric {
                                i.fas.fa-chart-bar  aria-hidden="true" {}
                                dl {
                                    dt {
                                        a href="/admin/reports/" { "Reports" }
                                    }
                                    dd { "Analytics" }
                                }
                                a href="/admin/reports/" aria-label="View reports" {
                                    i.fas.fa-chevron-right.fa-lg aria-hidden="true" {}
                                }
                            }
                        }
                    }
                }
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}
