#[approck::http(GET /admin/agent/?keyword=Option<String>&active=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        qs: QueryString,
    ) -> Result<Response> {
        use maud::html;

        doc.page_nav_add_record("Add New Agent", "/admin/agent/add");
        doc.set_title("Agent List");
        doc.set_body_display_fluid();

        use crate::api::admin::agent::list::admin_agent_list;
        let active = bux::parse_active_qs(&qs.active, Some(true));
        let output = admin_agent_list::call(
            app,
            identity,
            admin_agent_list::Input {
                keyword: qs.keyword.clone(),
                active,
            },
        )
        .await?;

        let mut dt = bux::component::detail_table(output.agent_list);

        dt.add_keyword_filter(qs.keyword.as_deref());
        dt.add_active_filter(active);

        dt.add_link_column(
            "Agent Name",
            |a| crate::ml_agent(a.agent_uuid),
            |a| format!("{} {}", a.first_name, a.last_name),
        );
        dt.add_column("ESID", |a| html! { (a.agent_esid) });
        dt.add_column(
            "Email",
            |a| html! {
                @if let Some(email) = &a.email {
                    a href=(format!("mailto:{}", email)) { (email) }
                } @else {
                    span.text-muted { "No email" }
                }
            },
        );
        dt.add_column(
            "Phone",
            |a| html! {
                @if let Some(phone) = &a.phone {
                    (phone)
                } @else {
                    span.text-muted { "No phone" }
                }
            },
        );
        dt.add_column(
            "Added On",
            |a| html! { (a.create_ts.format("%B %d, %Y").to_string()) },
        );
        dt.add_active_status_column("Agent Status", |a| a.active);
        dt.add_details_column(|a| crate::ml_agent(a.agent_uuid));

        doc.add_body(html!((dt)));

        Ok(Response::HTML(doc.into()))
    }
}
