#[approck::http(GET /admin/agent/{agent_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::api::admin::agent::detail::admin_agent_detail;
        use maud::html;

        let agent = admin_agent_detail::call(
            app,
            identity,
            admin_agent_detail::Input {
                agent_uuid: path.agent_uuid,
            },
        )
        .await?;

        doc.set_title("Agent Details");

        // Prepare display values
        let display_email = agent.email.as_deref().unwrap_or("Email not available");
        let display_phone = agent.phone.as_deref().unwrap_or("Phone not available");

        doc.add_body(html!(
            admin-agent-detail {
                grid-12 {
                    cell-3 {
                        panel {
                            content {
                                contact-info {
                                    h1 { (agent.name) }
                                    p.phone.mb-0 {
                                        (display_phone)
                                    }
                                    p.email {
                                        @if let Some(email) = &agent.email {
                                            a href=(format!("mailto:{}", email)) { (email) }
                                        } @else {
                                            (display_email)
                                        }
                                    }
                                    hr;
                                    @if agent.active {
                                        label-tag.success { "Active Agent" }
                                    } @else {
                                        label-tag.danger { "Inactive Agent" }
                                    }
                                }
                            }
                        }
                    }
                    cell-9 {
                        panel {
                            header {
                                h5 { "Agent Information" }
                            }
                            content {
                                grid-12 {
                                    cell-6 {
                                        table-wrapper.detail-list {
                                            table {
                                                tr {
                                                    th { "Agent ESID:" }
                                                    td { (agent.agent_esid) }
                                                }
                                                tr {
                                                    th { "First Name:" }
                                                    td { (agent.first_name) }
                                                }
                                                tr {
                                                    th { "Last Name:" }
                                                    td { (agent.last_name) }
                                                }
                                                tr {
                                                    th { "Email:" }
                                                    td {
                                                        @if let Some(email) = &agent.email {
                                                            a href=(format!("mailto:{}", email)) { (email) }
                                                        } @else {
                                                            span.text-muted { "Not provided" }
                                                        }
                                                    }
                                                }
                                                tr {
                                                    th { "Phone:" }
                                                    td {
                                                        @if let Some(phone) = &agent.phone {
                                                            (phone)
                                                        } @else {
                                                            span.text-muted { "Not provided" }
                                                        }
                                                    }
                                                }
                                                tr {
                                                    th { "Created:" }
                                                    td { (agent.create_ts.format("%B %d, %Y at %I:%M %p").to_string()) }
                                                }
                                            }
                                        }
                                    }
                                    cell-6 {
                                        table-wrapper.detail-list {
                                            table {
                                                @if let Some(address1) = &agent.address1 {
                                                    tr {
                                                        th { "Address:" }
                                                        td {
                                                            (address1)
                                                            @if let Some(address2) = &agent.address2 {
                                                                br;
                                                                (address2)
                                                            }
                                                        }
                                                    }
                                                }
                                                @if let Some(city) = &agent.city {
                                                    tr {
                                                        th { "City:" }
                                                        td { (city) }
                                                    }
                                                }
                                                @if let Some(state) = &agent.state {
                                                    tr {
                                                        th { "State:" }
                                                        td { (state) }
                                                    }
                                                }
                                                @if let Some(zip) = &agent.zip {
                                                    tr {
                                                        th { "ZIP Code:" }
                                                        td { (zip) }
                                                    }
                                                }
                                                @if let Some(country) = &agent.country {
                                                    tr {
                                                        th { "Country:" }
                                                        td { (country) }
                                                    }
                                                }
                                                tr {
                                                    th { "Status:" }
                                                    td {
                                                        @if agent.active {
                                                            label-tag.success { "Active" }
                                                        } @else {
                                                            label-tag.danger { "Inactive" }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                @if let Some(admin_note) = &agent.admin_note {
                    @if !admin_note.trim().is_empty() {
                        grid-12 {
                            cell-12 {
                                panel {
                                    header {
                                        h5 { "Additional Information" }
                                    }
                                    content {
                                        ul.data-list {
                                            li {
                                                hbox {
                                                    i.fas.fa-sticky-note aria-hidden="true" {}
                                                    dl {
                                                        dt { "Admin Note" }
                                                        dd {
                                                            (admin_note)
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}
