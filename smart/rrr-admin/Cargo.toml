[package]
name = "rrr-admin"
version = "0.1.0"
edition = "2024"

[package.metadata.acp]
module = {}
extends = ["rrr-zero", "approck", "bux", "granite", "auth-fence"]

[dependencies]
approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
approck-postgres = { workspace = true }

maud = { workspace = true }

rrr-zero = { path = "../rrr-zero" }
auth-fence = { workspace = true }
rand = { workspace = true }
chrono = { workspace = true }