[package]
name = "rrr-agent"
version = "0.1.0"
edition = "2024"

[package.metadata.acp]
module = {}
extends = ["rrr-zero", "approck", "bux", "granite", "auth-fence"]


[dependencies]
approck = {workspace = true}
auth-fence = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
approck-postgres = { workspace = true }
approck-redis = { workspace = true }

maud = { workspace = true }

rrr-zero = { path = "../rrr-zero" }

serde_json = { workspace = true }
serde = { workspace = true, features = ["derive"] }