#[approck::http(GET /agent/{agent_uuid:Uuid}/resources; AUTH None; return HTML | Redirect;)]
pub mod page {
    async fn request(doc: Document) -> Response {
        use maud::html;

        doc.add_body(html!(
            rrr-adv-resources."bux-narrow-75" {
                h1 { "The Real Return Reporter™ Advisor Resources" }
                p { "Use these resources to help you better understand and utilize the Real Return Reporter™ system effectively." }
                grid-2 {
                    panel {
                        header {
                            h5 { "RRR Analysis Report" }
                        }
                        content {
                            a href="https://asset7.net/Zagula/Smart/Debt2Capital/advisor-resources/GBU_FIA-REPORT.pdf" target="_blank" {
                                img src="https://asset7.net/Zagula/Smart/Debt2Capital/advisor-resources/rrr-analysis-cover-image.png" {}
                            }
                        }
                        footer {
                            a href="https://asset7.net/Zagula/Smart/Debt2Capital/advisor-resources/GBU_FIA-REPORT.pdf" target="_blank" { "View/Download" }
                        }
                    }
                    panel {
                        header {
                            h5 { "RRR Powerpoint" }
                        }
                        content {
                            a href="https://asset7.net/Zagula/Smart/Debt2Capital/advisor-resources/RRR+Pres+2025.pptx" target="_blank" {
                                img src="https://asset7.net/Zagula/Smart/Debt2Capital/advisor-resources/rrr-powerpoint-cover-image.png" {}
                            }
                        }
                        footer {
                            a href="https://asset7.net/Zagula/Smart/Debt2Capital/advisor-resources/RRR+Pres+2025.pptx" target="_blank" { "View/Download" }
                        }
                    }
                    panel {
                        header {
                            h5 { "Whitepaper Demo" }
                        }
                        content {
                            script src="https://fast.wistia.com/player.js" async {}
                            script src="https://fast.wistia.com/embed/rxkote9vj7.js" async type="module" {}
                            style { "wistia-player[media-id='rxkote9vj7']:not(:defined) { background: center / contain no-repeat url('https://fast.wistia.com/embed/medias/rxkote9vj7/swatch'); display: block; filter: blur(5px); padding-top:56.25%; }" }
                            wistia-player media-id="rxkote9vj7" aspect="1.7777777777777777" {}
                        }
                    }
                    panel {
                        header {
                            h5 { "Software Demo" }
                        }
                        content {
                            script src="https://fast.wistia.com/player.js" async {}
                            script src="https://fast.wistia.com/embed/vld3f2hg7u.js" async type="module" {}
                            style { "wistia-player[media-id='vld3f2hg7u']:not(:defined) { background: center / contain no-repeat url('https://fast.wistia.com/embed/medias/vld3f2hg7u/swatch'); display: block; filter: blur(5px); padding-top:56.25%; }" }
                            wistia-player media-id="vld3f2hg7u" aspect="1.7777777777777777" {}
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}
