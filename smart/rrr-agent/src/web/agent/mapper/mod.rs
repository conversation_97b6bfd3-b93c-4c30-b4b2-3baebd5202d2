pub mod index;
pub mod realrr;
pub mod resources;

#[approck::prefix(/agent/{agent_uuid:Uuid}/)]
pub mod prefix {
    pub fn menu(app: App, menu: Menu, agent_uuid: Uuid) {
        menu.set_label_name_uri(
            "Agent Dashboard",
            app.uuid_to_label(agent_uuid),
            &crate::ml_agent(agent_uuid),
        );
        menu.add_link("Real Return Reporter", &crate::ml_agent_realrr(agent_uuid));
        menu.add_link("Resources", &crate::ml_agent_resources(agent_uuid));
    }
}
