#[approck::http(GET /agent/{agent_uuid:Uuid}/; AUTH None; return HTML|Redirect;)]
pub mod page {
    async fn request(doc: Document) -> Result<Response> {
        use maud::html;

        doc.add_body(html!(
            #wrapper {
                h1 { "The Real Return Reporter™ Ageny Dashboard" }
                p { "Welcome to the Real Return Reporter™ Agent Dashboard." }
                p { "From here, you can access all of the tools and resources you need to effectively use the Real Return Reporter™ system." }

                (bux::button::link::label_icon_class("View Real Return Reporter", "fas fa-chart-line", "./realrr/", "primary"))
                " "
                (bux::button::link::label_icon_class("View Advisor Resources", "fas fa-book", "./resource", "secondary"))
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}
