export const MAX_RANGE = 40;
export const DEF_RANGE = 20;
export const MIN_RANGE = 1;

export type IndiceYears = Array<IndiceYear>;

// Since these are numeric types, they an represent either a percentage or a value, depending on the context
export type IndiceYear = {
    Year: number;
    DowJones: number | null;
    SP500: number | null;
    Nasdaq: number | null;
    SP100QQQ: number | null;
    BarclaysUSTech: number | null;
    BarclaysFortune500: number | null;
    BarclaysAgilityShield: number | null;
    AGG: number | null;
    FixedRate: number | null;
};

export type IndiceValues = {
    DowJones: number;
    SP500: number;
    Nasdaq: number;
    SP100QQQ: number;
    BarclaysUSTech: number;
    BarclaysFortune500: number;
    BarclaysAgilityShield: number;
    AGG: number;
    FixedRate: number;
};

export type IndiceSelection = {
    DowJones: boolean;
    SP500: boolean;
    Nasdaq: boolean;
    SP100QQQ: boolean;
    BarclaysUSTech: boolean;
    BarclaysFortune500: boolean;
    BarclaysAgilityShield: boolean;
    AGG: boolean;
    FixedRate: boolean;
};

// Define a struct of colors for each indice
export const IndiceColor = {
    DowJones: "#007BA7",
    SP500: "#EB4A46",
    Nasdaq: "#39bcb1",
    SP100QQQ: "#228B22",
    BarclaysUSTech: "#9400D3",
    BarclaysFortune500: "#F28500",
    BarclaysAgilityShield: "#FFD700",
    AGG: "#4E3C87",
    FixedRate: "#708090",
};

export function get_indice_selection_count(indice_selection: IndiceSelection): number {
    let count = 0;
    if (indice_selection.DowJones) count++;
    if (indice_selection.SP500) count++;
    if (indice_selection.Nasdaq) count++;
    if (indice_selection.SP100QQQ) count++;
    if (indice_selection.BarclaysUSTech) count++;
    if (indice_selection.BarclaysFortune500) count++;
    if (indice_selection.BarclaysAgilityShield) count++;
    if (indice_selection.AGG) count++;
    if (indice_selection.FixedRate) count++;
    return count;
}

export type IndiceQueryResult = {
    year_range: YearRange | null;
    year_selection: YearRange | null;
    indice_selection: IndiceSelection;
    indice_selection_count: number;
    indice_years_max: IndiceYears;
    indice_years_selected: IndiceYears;
};

export type CalcInput = {
    initial_balance: number | null;
    participation: number | null;
    barclays_annual_fee: number | null;
    standard_annual_fee: number | null;
    indice_selection: IndiceSelection;
    indice_years_selected: IndiceYears;
    contribute: AmountYearFromTo | null;
    withdrawal: AmountYearFromTo | null;
};

export type CalcOutput = {
    initial_balance: number;
    participation: number;
    barclays_annual_fee: number;
    standard_annual_fee: number;
    indice_selection: IndiceSelection;
    indice_selection_count: number;
    year_range: YearRange;
    projected_years: CalcOutputYear[];
    starting_balance: IndiceValues;
    average_annual_return: IndiceValues;
    actual_annual_return: IndiceValues;
    one_dollar_equals: IndiceValues;
    ending_balance: IndiceValues;
};

export class CalcError {
    message: string;

    constructor(message: string) {
        this.message = message;
    }
}

export type CalcOutputYear = {
    year: number;
    year_number: number;
    annual_return: IndiceValues;
    portfolio_value: IndiceValues;
    contribute_value: number | null;
    withdrawal_value: number | null;
};

/// represents the range of years to be displayed on the chart
export class YearRange {
    year_min: number;
    year_max: number;

    constructor(year_min: number, year_max: number) {
        this.year_min = year_min;
        this.year_max = year_max;
    }

    year_count(): number {
        return this.year_max - this.year_min + 1;
    }

    enumerate(): Iterable<[number, number]> {
        const year_min = this.year_min;
        const year_max = this.year_max;
        return {
            [Symbol.iterator]() {
                let year = year_min;
                let i = 0;
                return {
                    next() {
                        if (year <= year_max) {
                            return { value: [i++, year++], done: false };
                        }
                        // If the year exceeds the maximum, return done as true without needing an else clause
                        return { value: undefined, done: true };
                    },
                };
            },
        };
    }
}

export type AmountYearFromTo = { amount: number; year_from: number; year_to: number };
