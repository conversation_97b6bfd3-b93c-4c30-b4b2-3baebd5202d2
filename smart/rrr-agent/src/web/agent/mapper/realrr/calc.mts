import {
    Cal<PERSON><PERSON>rror,
    type CalcInput,
    type CalcOutput,
    type <PERSON>c<PERSON>utputYear,
    get_indice_selection_count,
    type IndiceValues,
    YearRange,
} from "./types.mts";

export function calculate(input: CalcInput): CalcOutput | CalcError {
    if (input.initial_balance === null) {
        return new CalcError("Initial balance is required.");
    }
    if (input.participation === null) {
        return new CalcError("Participation is required.");
    }
    if (input.barclays_annual_fee === null) {
        return new CalcError("Annual fee is required.");
    }
    if (input.standard_annual_fee === null) {
        return new CalcError("Standard Annual fee is required.");
    }
    if (get_indice_selection_count(input.indice_selection) === 0) {
        return new CalcError("At least one index must be selected.");
    }
    if (input.indice_years_selected.length === 0) {
        return new CalcError("At least one year must be selected.");
    }

    // translate CalcInput to CalcOutput

    const initial_balance = input.initial_balance;
    const participation = input.participation;
    const annual_fee = input.barclays_annual_fee;
    const standard_annual_fee = input.standard_annual_fee;
    const indice_selection = input.indice_selection;
    const indice_selection_count = get_indice_selection_count(indice_selection);
    const year_range = new YearRange(
        // biome-ignore lint/style/noNonNullAssertion: tested app logic
        input.indice_years_selected[0]!.Year,
        // biome-ignore lint/style/noNonNullAssertion: tested app logic
        input.indice_years_selected[input.indice_years_selected.length - 1]!.Year,
    );
    const projected_years: CalcOutputYear[] = [];

    const starting_balance: IndiceValues = {
        DowJones: initial_balance,
        SP500: initial_balance,
        Nasdaq: initial_balance,
        SP100QQQ: initial_balance,
        BarclaysUSTech: initial_balance,
        BarclaysFortune500: initial_balance,
        BarclaysAgilityShield: initial_balance,
        AGG: initial_balance,
        FixedRate: initial_balance,
    };

    // iterate over the input.indice_years_selected and calculate each year
    let previous_balance_daw: IndiceValues = starting_balance;
    let previous_balance_ror: IndiceValues = starting_balance;
    let year_number = 0;
    for (const indice_year of input.indice_years_selected) {
        const year = indice_year.Year;

        year_number++;

        let plusminus = 0;

        // determine contribution values
        let contribute_value = null;
        if (
            input.contribute !== null &&
            year_number >= input.contribute.year_from &&
            year_number <= input.contribute.year_to
        ) {
            contribute_value = input.contribute.amount;
            plusminus += contribute_value;
        }

        // determine withdrawal values
        let withdrawal_value = null;
        if (
            input.withdrawal !== null &&
            year_number >= input.withdrawal.year_from &&
            year_number <= input.withdrawal.year_to
        ) {
            withdrawal_value = input.withdrawal.amount;
            plusminus -= withdrawal_value;
        }

        // THIS IS USED FOR DEPOSIT AND WITHDRAWAL SIMULATION ONLY.  NO ROR CALCULATIONS.  IT IS ALSO DISPLAYED IN THE UI.
        // Note: Barclays indicies are the only ones that evaluate participation and annual fee & the rest get 100% participation and 0% annual fee
        const portfolio_value_daw: IndiceValues = {
            // Standard Annual Fee
            DowJones: new_balance_standard(
                previous_balance_daw.DowJones,
                indice_year.DowJones ?? 0,
                standard_annual_fee,
                plusminus,
            ),

            // Standard Annual Fee
            SP500: new_balance_standard(
                previous_balance_daw.SP500,
                indice_year.SP500 ?? 0,
                standard_annual_fee,
                plusminus,
            ),

            // Standard Annual Fee
            Nasdaq: new_balance_standard(
                previous_balance_daw.Nasdaq,
                indice_year.Nasdaq ?? 0,
                standard_annual_fee,
                plusminus,
            ),

            // Standard Annual Fee
            SP100QQQ: new_balance_standard(
                previous_balance_daw.SP100QQQ,
                indice_year.SP100QQQ ?? 0,
                standard_annual_fee,
                plusminus,
            ),

            // Standard Annual Fee
            AGG: new_balance_standard(
                previous_balance_daw.AGG,
                indice_year.AGG ?? 0,
                standard_annual_fee,
                plusminus,
            ),

            // send ACTUAL participation and annual fee
            BarclaysUSTech: new_balance_barclays(
                previous_balance_daw.BarclaysUSTech,
                indice_year.BarclaysUSTech ?? 0,
                participation,
                annual_fee,
                plusminus,
            ),

            // send ACTUAL participation and annual fee
            BarclaysFortune500: new_balance_barclays(
                previous_balance_daw.BarclaysFortune500,
                indice_year.BarclaysFortune500 ?? 0,
                participation,
                annual_fee,
                plusminus,
            ),

            BarclaysAgilityShield: new_balance_barclays(
                previous_balance_daw.BarclaysAgilityShield,
                indice_year.BarclaysAgilityShield ?? 0,
                participation,
                annual_fee,
                plusminus,
            ),

            // No Participation or Annual Fee
            FixedRate: new_balance_fixedrate(
                previous_balance_daw.FixedRate,
                indice_year.FixedRate ?? 0,
                plusminus,
            ),
        };

        // THIS IS USED FOR ROR CALCULATIONS ONLY.  NO DEPOSIT AND WITHDRAWAL VALUES ALLOWED.  DISCARDED AFTER USE.
        // Note: Barclays indicies are the only ones that evaluate participation and annual fee & the rest get 100% participation and 0% annual fee
        const portfolio_value_ror: IndiceValues = {
            // Standard Annual Fee
            DowJones: new_balance_standard(
                previous_balance_ror.DowJones,
                indice_year.DowJones ?? 0,
                standard_annual_fee,
                0,
            ),

            // Standard Annual Fee
            SP500: new_balance_standard(
                previous_balance_ror.SP500,
                indice_year.SP500 ?? 0,
                standard_annual_fee,
                0,
            ),

            // Standard Annual Fee
            Nasdaq: new_balance_standard(
                previous_balance_ror.Nasdaq,
                indice_year.Nasdaq ?? 0,
                standard_annual_fee,
                0,
            ),

            // Standard Annual Fee
            SP100QQQ: new_balance_standard(
                previous_balance_ror.SP100QQQ,
                indice_year.SP100QQQ ?? 0,
                standard_annual_fee,
                0,
            ),

            // Standard Annual Fee
            AGG: new_balance_standard(
                previous_balance_ror.AGG,
                indice_year.AGG ?? 0,
                standard_annual_fee,
                0,
            ),

            // send ACTUAL participation and annual fee
            BarclaysUSTech: new_balance_barclays(
                previous_balance_ror.BarclaysUSTech,
                indice_year.BarclaysUSTech ?? 0,
                participation,
                annual_fee,
                0,
            ),

            // send ACTUAL participation and annual fee
            BarclaysFortune500: new_balance_barclays(
                previous_balance_ror.BarclaysFortune500,
                indice_year.BarclaysFortune500 ?? 0,
                participation,
                annual_fee,
                0,
            ),

            BarclaysAgilityShield: new_balance_barclays(
                previous_balance_ror.BarclaysAgilityShield,
                indice_year.BarclaysAgilityShield ?? 0,
                participation,
                annual_fee,
                0,
            ),

            // No Participation or Annual Fee
            FixedRate: new_balance_fixedrate(
                previous_balance_ror.FixedRate,
                indice_year.FixedRate ?? 0,
                0,
            ),
        };

        // annual return is ((this year - last year) / last year) * 100
        const annual_return: IndiceValues = {
            DowJones: ((portfolio_value_ror.DowJones - previous_balance_ror.DowJones) /
                previous_balance_ror.DowJones) * 100,
            SP500: ((portfolio_value_ror.SP500 - previous_balance_ror.SP500) /
                previous_balance_ror.SP500) * 100,
            Nasdaq: ((portfolio_value_ror.Nasdaq - previous_balance_ror.Nasdaq) /
                previous_balance_ror.Nasdaq) * 100,
            SP100QQQ: ((portfolio_value_ror.SP100QQQ - previous_balance_ror.SP100QQQ) /
                previous_balance_ror.SP100QQQ) * 100,
            BarclaysUSTech:
                ((portfolio_value_ror.BarclaysUSTech - previous_balance_ror.BarclaysUSTech) /
                    previous_balance_ror.BarclaysUSTech) *
                100,
            BarclaysFortune500: ((portfolio_value_ror.BarclaysFortune500 -
                previous_balance_ror.BarclaysFortune500) /
                previous_balance_ror.BarclaysFortune500) *
                100,
            BarclaysAgilityShield: ((portfolio_value_ror.BarclaysAgilityShield -
                previous_balance_ror.BarclaysAgilityShield) /
                previous_balance_ror.BarclaysAgilityShield) *
                100,
            AGG: ((portfolio_value_ror.AGG - previous_balance_ror.AGG) / previous_balance_ror.AGG) *
                100,
            FixedRate: ((portfolio_value_ror.FixedRate - previous_balance_ror.FixedRate) /
                previous_balance_ror.FixedRate) *
                100,
        };

        projected_years.push({
            year: year,
            year_number: year_number,
            annual_return: annual_return,
            portfolio_value: portfolio_value_daw, // for the UI, we want the actual balances with deposits and withdrawals
            contribute_value: contribute_value,
            withdrawal_value: withdrawal_value,
        });

        // update the previous balances
        previous_balance_daw = portfolio_value_daw;
        previous_balance_ror = portfolio_value_ror;
    }

    // For purposes of ROR calulations, we do not want it affected by deposits and withdrawals.
    const ending_balance: IndiceValues = previous_balance_ror;

    // Calculate the average annual return
    const average_annual_return: IndiceValues = {
        DowJones: 0,
        SP500: 0,
        Nasdaq: 0,
        SP100QQQ: 0,
        BarclaysUSTech: 0,
        BarclaysFortune500: 0,
        BarclaysAgilityShield: 0,
        AGG: 0,
        FixedRate: 0,
    };

    for (const projected_year of projected_years) {
        average_annual_return.DowJones += projected_year.annual_return.DowJones;
        average_annual_return.SP500 += projected_year.annual_return.SP500;
        average_annual_return.Nasdaq += projected_year.annual_return.Nasdaq;
        average_annual_return.SP100QQQ += projected_year.annual_return.SP100QQQ;
        average_annual_return.BarclaysUSTech += projected_year.annual_return.BarclaysUSTech;
        average_annual_return.BarclaysFortune500 += projected_year.annual_return.BarclaysFortune500;
        average_annual_return.BarclaysAgilityShield +=
            projected_year.annual_return.BarclaysAgilityShield;
        average_annual_return.AGG += projected_year.annual_return.AGG;
        average_annual_return.FixedRate += projected_year.annual_return.FixedRate;
    }

    average_annual_return.DowJones /= projected_years.length;
    average_annual_return.SP500 /= projected_years.length;
    average_annual_return.Nasdaq /= projected_years.length;
    average_annual_return.SP100QQQ /= projected_years.length;
    average_annual_return.BarclaysUSTech /= projected_years.length;
    average_annual_return.BarclaysFortune500 /= projected_years.length;
    average_annual_return.BarclaysAgilityShield /= projected_years.length;
    average_annual_return.AGG /= projected_years.length;
    average_annual_return.FixedRate /= projected_years.length;

    // actual_annual_return = math.pow((future_value/present_value), (1/number_years)) - 1.0
    // * 100 is for converting to percentage

    const calc_actual_annual_return = (future_value: number, present_value: number): number => {
        return ((future_value / present_value) ** (1 / projected_years.length) - 1.0) * 100;
    };

    // Calculate the actual annual return
    const actual_annual_return: IndiceValues = {
        DowJones: calc_actual_annual_return(ending_balance.DowJones, starting_balance.DowJones),
        SP500: calc_actual_annual_return(ending_balance.SP500, starting_balance.SP500),
        Nasdaq: calc_actual_annual_return(ending_balance.Nasdaq, starting_balance.Nasdaq),
        SP100QQQ: calc_actual_annual_return(ending_balance.SP100QQQ, starting_balance.SP100QQQ),
        BarclaysUSTech: calc_actual_annual_return(
            ending_balance.BarclaysUSTech,
            starting_balance.BarclaysUSTech,
        ),
        BarclaysFortune500: calc_actual_annual_return(
            ending_balance.BarclaysFortune500,
            starting_balance.BarclaysFortune500,
        ),
        BarclaysAgilityShield: calc_actual_annual_return(
            ending_balance.BarclaysAgilityShield,
            starting_balance.BarclaysAgilityShield,
        ),
        AGG: calc_actual_annual_return(ending_balance.AGG, starting_balance.AGG),
        FixedRate: calc_actual_annual_return(ending_balance.FixedRate, starting_balance.FixedRate),
    };

    // Caclulate 1 dollar by dividing the ending balance by the starting balance
    const one_dollar_equals: IndiceValues = {
        DowJones: ending_balance.DowJones / starting_balance.DowJones,
        SP500: ending_balance.SP500 / starting_balance.SP500,
        Nasdaq: ending_balance.Nasdaq / starting_balance.Nasdaq,
        SP100QQQ: ending_balance.SP100QQQ / starting_balance.SP100QQQ,
        BarclaysUSTech: ending_balance.BarclaysUSTech / starting_balance.BarclaysUSTech,
        BarclaysFortune500: ending_balance.BarclaysFortune500 / starting_balance.BarclaysFortune500,
        BarclaysAgilityShield: ending_balance.BarclaysAgilityShield /
            starting_balance.BarclaysAgilityShield,
        AGG: ending_balance.AGG / starting_balance.AGG,
        FixedRate: ending_balance.FixedRate / starting_balance.FixedRate,
    };

    const calc_output: CalcOutput = {
        initial_balance: initial_balance,
        participation: participation,
        barclays_annual_fee: annual_fee,
        standard_annual_fee: standard_annual_fee,
        indice_selection: indice_selection,
        indice_selection_count: indice_selection_count,
        year_range: year_range,
        projected_years: projected_years,
        starting_balance: starting_balance,
        average_annual_return: average_annual_return,
        actual_annual_return: actual_annual_return,
        ending_balance: ending_balance,
        one_dollar_equals: one_dollar_equals,
    };

    return calc_output;
}

function new_balance_fixedrate(balance: number, annual_return: number, plusminus: number): number {
    // adjust balance
    balance += plusminus;

    const annual_return_decimal = annual_return / 100;
    const increase = balance * annual_return_decimal;

    balance += increase;

    return balance;
}

function new_balance_barclays(
    balance: number,
    annual_return: number,
    participation: number,
    annual_fee: number,
    plusminus: number,
): number {
    // adjust balance
    balance += plusminus;

    // define some constants
    const annual_return_decimal = annual_return / 100;
    const annual_fee_decimal = annual_fee / 100;
    const participation_decimal = participation / 100;

    // first we subtract the fee
    const fee = balance * annual_fee_decimal;
    balance -= fee;

    // If the annual return goes up, the we take the participation percentage of that
    if (annual_return_decimal >= 0) {
        const increase = balance * annual_return_decimal * participation_decimal;
        balance += increase;
    } // If it goes down, we don't lose anything

    return balance;
}
function new_balance_standard(
    balance: number,
    annual_return: number,
    standard_annual_fee: number,
    plusminus: number,
): number {
    // adjust balance
    balance += plusminus;

    // define some constants
    const annual_return_decimal = annual_return / 100;
    const standard_annual_fee_decimal = standard_annual_fee / 100;

    // first we subtract the fee
    const fee = balance * standard_annual_fee_decimal;
    balance -= fee;

    const increase = balance * annual_return_decimal;
    balance += increase;

    return balance;
}
