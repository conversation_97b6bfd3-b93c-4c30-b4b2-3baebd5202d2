import "./index.mcss";

// vim:fileencoding=utf-8:ts=4:sw=4:sts=4:expandtab
import { calculate } from "./calc.mts";
import { Chart } from "./chart.mts";
import { data_init, indice_query } from "./data.mts";
import { inputs_init } from "./inputs.mts";
import { outputs_init } from "./outputs.mts";
import { CalcError, type CalcInput, type IndiceSelection, type IndiceYears } from "./types.mts";

const data: IndiceYears = (globalThis as any).script_data;
init(data);

// Exporting a function named 'init'
function init(IndiceInputData: IndiceYears): void {
    // Setup the data engine
    data_init(IndiceInputData);

    // Get the input wrappers from the dom
    const inputs = inputs_init();
    const outputs = outputs_init();

    // Setup the chart
    const chart = new Chart("mts-chart");

    // refresh function coordinates errors and updates
    function refresh() {
        // generate a query
        const indice_selection: IndiceSelection = {
            DowJones: inputs.tb_dow_jones.get_value(),
            SP500: inputs.tb_sp_500.get_value(),
            Nasdaq: inputs.tb_nasdaq.get_value(),
            SP100QQQ: inputs.tb_invesco.get_value(),
            BarclaysUSTech: inputs.tb_us_tech.get_value(),
            BarclaysFortune500: inputs.tb_fortune_500.get_value(),
            BarclaysAgilityShield: inputs.tb_agility_shield.get_value(),
            AGG: inputs.tb_agg.get_value(),
            FixedRate: inputs.tb_fixed_rate.get_value(),
        };
        const query_result = indice_query(
            indice_selection,
            inputs.fixed_rate.get_value(),
            inputs.year_range.get_selected_year_range(),
        );

        // update the sources (web links)
        outputs.sources.refresh(indice_selection);

        // enable the flashing background if there are no selections
        inputs.toggle_button_background.set(query_result.indice_selection_count === 0);

        // set the year range
        inputs.year_range.set_allowed_year_range(query_result.year_range);
        inputs.year_range.set_selected_year_range(query_result.year_selection);

        // draw the chart
        chart.draw(query_result);

        // prepare calc input
        const calc_input: CalcInput = {
            initial_balance: inputs.initial_balance.get_value(),
            participation: inputs.participation.get_value(),
            barclays_annual_fee: inputs.annual_fee.get_value(),
            standard_annual_fee: inputs.standard_annual_fee.get_value(),
            indice_selection: indice_selection,
            indice_years_selected: query_result.indice_years_selected,
            contribute: inputs.contribute.get_value(),
            withdrawal: inputs.withdrawal.get_value(),
        };

        console.log("Calc Input", calc_input);
        const calc_output = calculate(calc_input);

        // Console Log for auditing
        if (calc_output instanceof CalcError) {
            console.log("Calc Error", calc_output.message);
        } else {
            console.log("Calc Output Input Years");
            console.table(calc_input.indice_years_selected);
            console.log("Calc Output Output Annual Return");
            console.table(calc_output.average_annual_return);
            console.log("Calc Output Output Actual Return");
            console.table(calc_output.actual_annual_return);
            console.log("Calc Output Output One Dollar Equals");
            console.table(calc_output.one_dollar_equals);
            console.log("Calc Output Output Projected Years");
            console.table(calc_output.projected_years);
        }

        // draw the outputs
        outputs.stats_table.draw(calc_output);
        outputs.performance_chart.draw(calc_output);
        outputs.performance_table.draw(calc_output);
    }

    // bind event handlers
    inputs.initial_balance.on_change(refresh);
    inputs.participation.on_change(refresh);
    inputs.annual_fee.on_change(refresh);
    inputs.standard_annual_fee.on_change(refresh);
    inputs.fixed_rate.on_change(refresh);

    inputs.tb_dow_jones.on_change(refresh);
    inputs.tb_sp_500.on_change(refresh);
    inputs.tb_nasdaq.on_change(refresh);
    inputs.tb_invesco.on_change(refresh);
    inputs.tb_us_tech.on_change(refresh);
    inputs.tb_fortune_500.on_change(refresh);
    inputs.tb_agility_shield.on_change(refresh);
    inputs.tb_agg.on_change(refresh);
    inputs.tb_fixed_rate.on_change(refresh);

    inputs.year_range.on_change(refresh);
    inputs.contribute.on_change(refresh);
    inputs.withdrawal.on_change(refresh);

    inputs.clear2.addEventListener("click", (event) => {
        event.preventDefault();
        inputs.contribute.clear();
        inputs.withdrawal.clear();
    });

    // kick it off with a call to refresh
    refresh();
}
