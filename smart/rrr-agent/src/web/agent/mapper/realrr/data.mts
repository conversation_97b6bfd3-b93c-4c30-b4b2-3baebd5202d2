import {
    DEF_RANGE,
    type IndiceQueryResult,
    type IndiceSelection,
    type IndiceYear,
    type IndiceYears,
    MAX_RANGE,
    MIN_RANGE,
    YearRange,
} from "./types.mts";

type State = {
    indice_years: IndiceYears;
};

const state: State = {
    indice_years: [],
};

export function data_init(data: IndiceYear[]) {
    state.indice_years.push(...data);
}

export function indice_query(
    indice_selection: IndiceSelection,
    fixed_rate: number | null,
    year_selection_from_ui: YearRange | null,
): IndiceQueryResult {
    let indice_selection_count = 0;
    if (indice_selection.DowJones) {
        indice_selection_count++;
    }
    if (indice_selection.SP500) {
        indice_selection_count++;
    }
    if (indice_selection.Nasdaq) {
        indice_selection_count++;
    }
    if (indice_selection.SP100QQQ) {
        indice_selection_count++;
    }
    if (indice_selection.BarclaysUSTech) {
        indice_selection_count++;
    }
    if (indice_selection.BarclaysFortune500) {
        indice_selection_count++;
    }
    if (indice_selection.BarclaysAgilityShield) {
        indice_selection_count++;
    }
    if (indice_selection.AGG) {
        indice_selection_count++;
    }
    if (indice_selection.FixedRate) {
        indice_selection_count++;
    }

    // loop over state.indice_years
    const indice_years_max: IndiceYears = [];
    for (const indice_year of state.indice_years) {
        let include_it = indice_selection.DowJones ||
            indice_selection.SP500 ||
            indice_selection.Nasdaq ||
            indice_selection.SP100QQQ ||
            indice_selection.BarclaysUSTech ||
            indice_selection.BarclaysFortune500 ||
            indice_selection.BarclaysAgilityShield ||
            indice_selection.AGG ||
            indice_selection.FixedRate;

        if (indice_year.DowJones === null && indice_selection.DowJones) {
            include_it = false;
        }
        if (indice_year.SP500 === null && indice_selection.SP500) {
            include_it = false;
        }
        if (indice_year.Nasdaq === null && indice_selection.Nasdaq) {
            include_it = false;
        }
        if (indice_year.SP100QQQ === null && indice_selection.SP100QQQ) {
            include_it = false;
        }
        if (indice_year.BarclaysUSTech === null && indice_selection.BarclaysUSTech) {
            include_it = false;
        }
        if (indice_year.BarclaysFortune500 === null && indice_selection.BarclaysFortune500) {
            include_it = false;
        }
        if (indice_year.BarclaysAgilityShield === null && indice_selection.BarclaysAgilityShield) {
            include_it = false;
        }
        if (indice_year.AGG === null && indice_selection.AGG) {
            include_it = false;
        }

        if (!include_it) {
            continue;
        }

        // create a new indice_year object as clone, but, include the passed fixedrate value here
        const indice_year_new: IndiceYear = {
            Year: indice_year.Year,
            DowJones: indice_year.DowJones,
            SP500: indice_year.SP500,
            Nasdaq: indice_year.Nasdaq,
            SP100QQQ: indice_year.SP100QQQ,
            BarclaysUSTech: indice_year.BarclaysUSTech,
            BarclaysFortune500: indice_year.BarclaysFortune500,
            BarclaysAgilityShield: indice_year.BarclaysAgilityShield,
            AGG: indice_year.AGG,
            FixedRate: fixed_rate,
        };

        indice_years_max.push(indice_year_new);
    }

    let year_range: YearRange | null = null;
    if (indice_years_max.length > 0) {
        // biome-ignore lint/style/noNonNullAssertion: tested app logic
        year_range = new YearRange(
            indice_years_max[0]!.Year,
            indice_years_max[indice_years_max.length - 1]!.Year,
        );
    }

    let year_selection: YearRange | null = null;
    if (year_range !== null) {
        if (year_selection_from_ui !== null) {
            let year_count = year_selection_from_ui.year_count();
            let year_max = year_range.year_max;

            // if the year_max is in the year_range, then use it, otherwise reset it to max
            if (
                year_selection_from_ui.year_max >= year_range.year_min &&
                year_selection_from_ui.year_max <= year_range.year_max
            ) {
                year_max = year_selection_from_ui.year_max;
            }

            if (year_count > MAX_RANGE) {
                year_count = MAX_RANGE;
            } else if (year_count < MIN_RANGE) {
                year_count = MIN_RANGE;
            }
            year_selection = new YearRange(year_max - year_count + 1, year_max);
        } else {
            year_selection = new YearRange(
                Math.max(year_range.year_min, year_range.year_max - DEF_RANGE + 1),
                year_range.year_max,
            );
        }
    }

    const indice_years_selected: IndiceYears = [];
    // copy all the rows from indice_years_max that are in the year_selection
    if (year_selection !== null) {
        for (const indice_year of indice_years_max) {
            if (
                indice_year.Year >= year_selection.year_min &&
                indice_year.Year <= year_selection.year_max
            ) {
                indice_years_selected.push(indice_year);
            }
        }
    }

    const rval: IndiceQueryResult = {
        year_range,
        year_selection,
        indice_selection,
        indice_selection_count,
        indice_years_max,
        indice_years_selected,
    };

    return rval;
}
