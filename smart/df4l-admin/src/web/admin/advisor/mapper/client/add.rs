#[approck::http(GET /admin/advisor/{advisor_uuid:Uuid}/client/add; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, doc: Document, path: Path) -> Result<Response> {
        use maud::html;

        doc.set_title("Add Client");

        let mut form_panel = bux::component::add_cancel_form_panel(
            "Add New Client",
            &crate::ml_admin_advisor_client_list(path.advisor_uuid),
        );

        //add hidden field for advisor_uuid
        form_panel.set_hidden("advisor_uuid", path.advisor_uuid);

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            grid-12 {
                cell-6 {
                    (bux::input::text::string::name_label_value("first_name", "Client First Name:", None))
                    (bux::input::text::string::name_label_value("last_name", "Client Last Name:", None))
                    (bux::input::text::string::name_label_value("email", "Email:", None))
                    (bux::input::text::string::name_label_value_help("phone", "Send Text Messages To Phone Number", None, "Do not include country code."))
                }
                cell-6 {
                    (bux::input::text::string::name_label_value("address1", "Address Line 1", None))
                    (bux::input::text::string::name_label_value("address2", "Address Line 2", None))
                    grid-3 {
                        (bux::input::text::string::name_label_value("city", "City", None))
                        (addr_iso::input::address_us_select::us_state_select_with_help(app, "state", "State", None, "").await?)
                        (bux::input::text::string::name_label_value("zip", "ZIP", None))
                    }
                    (bux::input::textarea::string::name_label_value("note", "Note:", None))

                }
            }
        ));
        doc.add_body(html! {
            section {
                (form_panel)
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}
