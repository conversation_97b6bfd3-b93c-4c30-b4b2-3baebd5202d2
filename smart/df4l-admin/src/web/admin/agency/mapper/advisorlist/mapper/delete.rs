#[approck::http(GET /admin/agency/{agency_uuid:Uuid}/advisorlist/{advisor_uuid:Uuid}/delete; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::api::admin::advisor::detail::admin_advisor_detail as advisor_detail;
        use crate::api::admin::agency::detail::admin_agency_detail;
        use maud::html;

        // Get agency details
        let agency = admin_agency_detail::call(
            app,
            identity,
            admin_agency_detail::Input {
                agency_uuid: path.agency_uuid,
            },
        )
        .await?;

        // Get advisor details
        let advisor = advisor_detail::call(
            app,
            identity,
            advisor_detail::Input {
                advisor_uuid: path.advisor_uuid,
            },
        )
        .await?;

        doc.set_title("Remove Advisor");

        let title = format!(
            "Remove Advisor {} {} ({}) From Agency: {}?",
            advisor.first_name, advisor.last_name, advisor.advisor_esid, agency.name
        );

        let mut panel = bux::component::delete_cancel_form_panel(
            &title,
            &crate::ml_agency_advisorlist(path.agency_uuid),
        );
        panel.set_hidden("agency_uuid", path.agency_uuid.to_string());
        panel.set_hidden("advisor_uuid", path.advisor_uuid.to_string());

        #[rustfmt::skip]
        panel.add_body(maud::html!(
            p { "This will remove the advisor from this agency. The advisor will still exist in the system but will no longer be associated with this agency." }
            p { "Any clients assigned to this advisor will remain assigned to them." }
            (bux::input::checkbox::name_label_checked("confirm", "I understand the above.", false))
        ));

        doc.add_body(html!(
            section {
                (panel)
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}
