pub mod api;
pub mod core;
pub mod web;

pub trait App:
    approck::App
    + approck_redis::App
    + auth_fence::App
    + api_stripe::App
    + api_sendgrid::App
    + api_twilio::App
    + legal_plane::App
{
}

pub trait Identity: approck::Identity + auth_fence::Identity + legal_plane::Identity {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }

    fn signup_advisor_create(&self) -> bool {
        true
    }

    fn get_address(&self) -> String {
        self.remote_addr().to_string()
    }

    #[allow(async_fn_in_trait)]
    async fn signup_advisor_read(
        &self,
        dbcx: &impl approck_postgres::DB,
        appsignup_advisor_uuid: granite::Uuid,
    ) -> bool;

    #[allow(async_fn_in_trait)]
    async fn signup_advisor_write(
        &self,
        dbcx: &impl approck_postgres::DB,
        appsignup_advisor_uuid: granite::Uuid,
    ) -> bool;

    fn remote_addr(&self) -> std::net::IpAddr;
}

pub trait Document: bux::document::Cliffy {}
pub trait DocumentPublic: bux::document::Base {}

pub fn ml_signup_advisor(signup_advisor_uuid: granite::Uuid) -> String {
    format!("/signup/advisor/{}/", signup_advisor_uuid)
}
