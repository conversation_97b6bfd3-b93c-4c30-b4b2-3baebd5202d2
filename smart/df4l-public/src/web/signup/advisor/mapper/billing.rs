#[approck::http(GET /signup/advisor/{signup_advisor_uuid:Uuid}/billing; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        doc: Document,
        identity: Identity,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use crate::api::signup::advisor::signup_advisor_stripe_data;
        use crate::api::signup::advisor::signup_advisor_wizard_data;
        use approck::html;

        struct StripeSubscription {
            is_paid: bool,
            subscription_id: Option<String>,
            portal_link: Option<String>,
            error_message: String,
        }
        let mut stripe_subscription = StripeSubscription {
            is_paid: false,
            subscription_id: None,
            portal_link: None,
            error_message: "Looks like something is wrong with your subscription. Please contact support if this error persists.".to_string(),
        };

        let wizard_data = signup_advisor_wizard_data::call(
            app,
            identity,
            signup_advisor_wizard_data::Input {
                signup_advisor_uuid: path.signup_advisor_uuid,
            },
        )
        .await?;
        let stripe_data = signup_advisor_stripe_data::call(
            app,
            identity,
            signup_advisor_stripe_data::Input {
                signup_advisor_uuid: path.signup_advisor_uuid,
            },
        )
        .await?;

        // If stripe is marked as Paid inside DB, we do not need to deal with session at all
        if stripe_data.stripe_payment_status == Some("paid".to_string()) {
            stripe_subscription.is_paid = true;
            stripe_subscription.subscription_id = stripe_data.stripe_subscription_id;
        } else {
            // load stripe session object from stripe api
            let session_id = stripe_data.stripe_checkout_session_id.unwrap_or_default();
            println!("DB - stripe_data: session_id: {}", session_id);

            //we need to check if we have a session id, and get checkout_session from stripe
            let checkout_session = if session_id.is_empty() {
                None
            } else {
                Some(app.stripe().checkout_session_get(&session_id).await?)
            };
            let payment_status = checkout_session
                .as_ref()
                .map(|s| s.payment_status.clone())
                .unwrap_or_default();

            // Create local variables from checkout_session data just for clarity
            let checkout_session_id = checkout_session.as_ref().map(|s| s.id.clone());
            let stripe_subscription_id = checkout_session.as_ref().map(|s| s.subscription.clone());

            // Update stripe_subscription object
            stripe_subscription.subscription_id =
                stripe_subscription_id.clone().unwrap_or_default();

            println!("STRIPE checkout_session: {:?}", checkout_session);
            //println!("checkout_session.id: {:?}", checkout_session_id);
            //println!("checkout_session.payment_status: {:?}", payment_status);

            //update signup_advisor with stripe_checkout_session_id and stripe_payment_status
            let dbcx = app.postgres_dbcx().await?;
            granite::pg_execute!(
                db = dbcx;
                args = {
                    $signup_advisor_uuid: &path.signup_advisor_uuid,
                    $stripe_checkout_session_id: &checkout_session_id,
                    $stripe_payment_status: &payment_status,
                    $stripe_subscription_id: &stripe_subscription_id,
                };
                UPDATE
                    df4l.signup_advisor
                SET
                    stripe_checkout_session_id = $stripe_checkout_session_id,
                    stripe_payment_status = $stripe_payment_status,
                    stripe_subscription_id = $stripe_subscription_id,
                    payment_completed_ts = NOW()
                WHERE
                    signup_advisor_uuid = $signup_advisor_uuid
            )
            .await?;

            // if payment_status is paid, we don't need to show the button
            if payment_status == "paid" {
                stripe_subscription.is_paid = true;
            }
        } // if

        if stripe_data.stripe_customer_id.is_some() {
            // Update stripe_subscription object
            stripe_subscription.portal_link = Some(
                app.stripe()
                    .create_billing_portal_link(
                        stripe_data
                            .stripe_customer_id
                            .as_deref()
                            .unwrap_or_default(),
                        &format!(
                            "{}/signup/advisor/{}/billing",
                            "https://local.acp7.net:3014", // TODO
                            path.signup_advisor_uuid
                        ),
                        None,
                    )
                    .await?
                    .url,
            );
        }

        let product_id = "prod_STrGOHNTdsTTpO"; //TODO move it to LOCAL.toml 
        let price_id = "price_1RYtYHH1mpXpEHoK4xOl7H6F"; //TODO move it to LOCAL.toml
        let metered_price_id = "price_1RYts6H1mpXpEHoKJ8Np2Jxh"; //TODO move it to LOCAL.toml

        let product = app.stripe().product_get(product_id).await?;
        //println!("STRIPE product: {:?}", product);
        let product_name = product.name;

        let price = app.stripe().price_get(price_id).await?;
        //println!("STRIPE price: {:?}", price);
        let price_amount = price.unit_amount.unwrap_or_default();
        let price_amount = price_amount / 100;

        let metered_price = app.stripe().price_get(metered_price_id).await?;
        //println!("STRIPE metered_price: {:?}", metered_price);
        let metered_price_amount = metered_price.unit_amount.unwrap_or_default();
        let metered_price_amount = metered_price_amount / 100;

        let mut wizard = bux::component::form_wizard::new(WizardStep::Billing, wizard_data)?;
        wizard.set_id("billing-form");
        wizard.set_hidden("action", "billing_info");
        wizard.set_hidden("signup_advisor_uuid", path.signup_advisor_uuid);

        wizard.add_body(html! {
            // show this button if we don't have checkout_session
            @if stripe_subscription.is_paid {
                div.x-success-wrapper {
                    p { "Your subscription has been successfully activated." }
                    hr;
                    @if let (Some(subscription_id), Some(portal_link)) = (&stripe_subscription.subscription_id, &stripe_subscription.portal_link) {
                        p { "Your subscription ID is: " (subscription_id) }
                        p { "You are now fully set up." }
                        p { "To manage your subscription, please use the link below." }

                        a href=(portal_link) { "Manage Your Subscription" }
                    }
                    @else {
                        p { (stripe_subscription.error_message) }
                    }
                }
            }
            @else {
                div.credit-card-form {
                    // Create Customer Link with id="create-customer"
                    div {
                        p { "You are signing up for:" }
                        p { b { (product_name) " - $" (price_amount) " per month, plus $" (metered_price_amount) " per credit report" } }
                        p { "Please click the button below to continue." }
                        a.btn.success id="call-stripe" {
                            i.fas.fa-credit-card aria-hidden="true" {}
                            " "
                            span { "Continue To Billing" }
                        }
                    }
                }
            }
        });

        doc.set_title("Billing Information");
        doc.hide_page_nav();
        doc.add_body(html! {
            (wizard)
        });

        Ok(Response::HTML(doc.into()))
    }
}
