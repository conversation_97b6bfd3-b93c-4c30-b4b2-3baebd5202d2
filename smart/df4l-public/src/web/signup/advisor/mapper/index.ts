//-------------------------------------------------------------------------------------------------
// 1. Import Components

import "./index.mcss";
import "@bux/input/text/string.mts";
import "@bux/component/form_wizard.mts";

// -------------------------------------------------------------------------------------------------
// 2. Import Code

import { SE, SEC } from "@granite/lib.mts";
import { signup_advisor_contact_set } from "@crate/api/signup/advisorλ.mts";
import BuxInputTextString from "@bux/input/text/string.mts";
import FormWizard from "@bux/component/form_wizard.mts";

// -------------------------------------------------------------------------------------------------
// 3. Find Elements
const $form = SE(document, "form.bux-form-wizard") as HTMLFormElement;

// Entire functionality of page conditional on this element existing.
if ($form.querySelector("content")) {
    const $signup_advisor_uuid: HTMLInputElement = SE($form, "[name=signup_advisor_uuid]");
    const $first_name: BuxInputTextString = SE($form, "[name=first_name]");
    const $last_name: BuxInputTextString = SE($form, "[name=last_name]");
    const $email: BuxInputTextString = SE($form, "[name=email]");
    const $phone: BuxInputTextString = SE($form, "[name=phone]");

    const signup_advisor_uuid = $signup_advisor_uuid.value;

    const $next_button = SEC(HTMLAnchorElement, $form, "a.x-next-button");

    // -------------------------------------------------------------------------------------------------
    // 4. Bind Event Handlers

    $next_button.addEventListener("click", (event) => {
        event.preventDefault();
        $form.dispatchEvent(new Event("submit"));
    });

    // -------------------------------------------------------------------------------------------------
    // 5. Write Code
    // -------------------------------------------------------------------------------------------------

    new FormWizard({
        $form,
        api: signup_advisor_contact_set.api,

        err: (errors) => {
            $first_name.set_e(errors.first_name);
            $last_name.set_e(errors.last_name);
            $email.set_e(errors.email);
            $phone.set_e(errors.phone);
        },

        get: () => {
            return {
                signup_advisor_uuid: signup_advisor_uuid,
                first_name: $first_name.value,
                last_name: $last_name.value,
                email: $email.value,
                phone: $phone.value,
            };
        },

        set: (_value) => {
        },

        out: (_output) => {
            window.location.href = $next_button.href;
        },
    });
}
