//-------------------------------------------------------------------------------------------------
// 1. Import Components
import "./billing.mcss";
import "@bux/input/text/string.mts";
import "@bux/component/form_wizard.mts";

// -------------------------------------------------------------------------------------------------
// 2. Import Code
import { SE_nullable, SEC } from "@granite/lib.mts";
import { signup_advisor_billing_save } from "@crate/api/signup/advisorλ.mts";
// -------------------------------------------------------------------------------------------------

// 3. Find Elements
const $form = SEC(HTMLFormElement, document, "#billing-form");
const signup_advisor_uuid = SEC(HTMLInputElement, $form, "[name=signup_advisor_uuid]").value;

// Entire functionality of page conditional on this element existing.
if ($form.querySelector("content")) {
    const $call_stripe: HTMLAnchorElement | null = SE_nullable<HTMLAnchorElement>(
        $form,
        "[id=call-stripe]",
    );
    // -------------------------------------------------------------------------------------------------
    // 4. Bind Event Handlers

    // -------------------------------------------------------------------------------------------------
    // 5. Write Code
    if ($call_stripe) {
        $call_stripe.addEventListener("click", (event) => {
            event.preventDefault();
            call_stripe();
        });
    }
}

async function call_stripe() {
    const response = await signup_advisor_billing_save.api.call({
        signup_advisor_uuid: signup_advisor_uuid,
    });

    if ("ValidationError" in response) {
        const errors = response.ValidationError[0];

        if ("Inner" in errors) {
            const inner_errors = errors.Inner as any;
            // TODO: Display errors
            console.log(inner_errors);
        }
        if ("Outer" in errors) {
            alert(errors.Outer);
        }
    }
    if ("Output" in response) {
        const output = response.Output[0];
        window.location.href = output.url;
    }
}
