#[approck::http(GET /signup/advisor/{signup_advisor_uuid:Uuid}/verify; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        doc: Document,
        identity: Identity,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use crate::api::signup::advisor::{signup_advisor_verify_get, signup_advisor_wizard_data};
        use approck::html;

        let wizard_data = signup_advisor_wizard_data::call(
            app,
            identity,
            signup_advisor_wizard_data::Input {
                signup_advisor_uuid: path.signup_advisor_uuid,
            },
        )
        .await?;

        // this is where the verification codes get sent out.
        let verify_data = signup_advisor_verify_get::call(
            app,
            identity,
            signup_advisor_verify_get::Input {
                signup_advisor_uuid: path.signup_advisor_uuid,
            },
        )
        .await?;

        let phone_component = {
            let mut p = bux::component::verification_code::new_phone_4(&verify_data.phone);
            p.set_verified(verify_data.phone_verified);
            p
        };

        let email_component = {
            let mut e = bux::component::verification_code::new_email_4(&verify_data.email);
            e.set_verified(verify_data.email_verified);
            e
        };

        #[rustfmt::skip]
        let wizard = {
            let mut wizard = bux::component::form_wizard::new(WizardStep::Verify, wizard_data)?;
            wizard.set_id("verification-form");
            wizard.set_hidden("signup_advisor_uuid", path.signup_advisor_uuid);
            wizard.add_body(html! {
                grid-2 {
                    div.x-phone-wrapper {
                        (phone_component)
                    }
                    div.x-email-wrapper {
                        (email_component)
                    }
                }
            });
            wizard
        };

        doc.set_title("Verification");
        doc.hide_page_nav();
        doc.add_body(html!((wizard)));
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod signup_advisor_verify_phone {
    use maud::Render;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_advisor_uuid: Uuid,
        pub phone_code: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub inner_html: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        use crate::api::signup::advisor::signup_advisor_verify_phone;

        // SECON PASSTHROUGH
        let output = signup_advisor_verify_phone::call(
            app,
            identity,
            signup_advisor_verify_phone::Input {
                signup_advisor_uuid: input.signup_advisor_uuid,
                phone_code: input.phone_code.clone(),
            },
        )
        .await?;

        let phone_component = {
            let mut p = bux::component::verification_code::new_phone_4(&output.phone);
            match output.status {
                signup_advisor_verify_phone::Status::Verified => {
                    p.set_verified(true);
                }
                signup_advisor_verify_phone::Status::Error(ref message) => {
                    p.set_user_error(message);
                }
            }
            p
        };

        Ok(Output {
            inner_html: phone_component.render().into_string(),
        })
    }
}

#[approck::api]
pub mod signup_advisor_verify_email {
    use crate::api::signup::advisor::signup_advisor_verify_email;
    use maud::Render;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_advisor_uuid: Uuid,
        pub email_code: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub inner_html: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        // SECON PASSTHROUGH
        let output = signup_advisor_verify_email::call(
            app,
            identity,
            signup_advisor_verify_email::Input {
                signup_advisor_uuid: input.signup_advisor_uuid,
                email_code: input.email_code.clone(),
            },
        )
        .await?;

        let email_component = {
            let mut e = bux::component::verification_code::new_email_4(&output.email);
            match output.status {
                signup_advisor_verify_email::Status::Verified => {
                    e.set_verified(true);
                }
                signup_advisor_verify_email::Status::Error(ref message) => {
                    e.set_user_error(message);
                }
            }
            e
        };

        Ok(Output {
            inner_html: email_component.render().into_string(),
        })
    }
}
