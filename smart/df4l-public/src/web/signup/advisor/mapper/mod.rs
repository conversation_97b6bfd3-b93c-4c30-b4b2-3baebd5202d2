pub mod billing;
pub mod index;
pub mod password;
pub mod terms;
pub mod verify;

#[approck::prefix(/signup/advisor/{signup_advisor_uuid:Uuid}/)]
pub mod prefix {
    pub fn menu(menu: Menu, signup_advisor_uuid: Uuid) {
        menu.set_label_uri(
            "Advisor Signup",
            &format!("/signup/advisor/{}/", signup_advisor_uuid),
        );
    }
}

use bux::component::form_wizard::FormWizardImpl;

#[derive(PartialEq, PartialOrd)]
pub enum WizardStep {
    Index,
    Verify,
    Terms,
    Billing,
    Password,
}

#[allow(refining_impl_trait)]
impl FormWizardImpl for WizardStep {
    type Context = crate::api::signup::advisor::signup_advisor_wizard_data::Output;

    fn all_variants() -> Vec<Self> {
        vec![
            WizardStep::Index,
            WizardStep::Verify,
            WizardStep::Terms,
            WizardStep::Billing,
            WizardStep::Password,
        ]
    }

    fn label(&self, _context: &Self::Context) -> String {
        match self {
            WizardStep::Index => "Contact Information".to_string(),
            WizardStep::Verify => "Verification".to_string(),
            WizardStep::Terms => "Terms & Conditions".to_string(),
            WizardStep::Billing => "Payment".to_string(),
            WizardStep::Password => "Create Password".to_string(),
        }
    }

    fn href(&self, context: &Self::Context) -> String {
        let uuid = context.signup_advisor_uuid;
        match self {
            WizardStep::Index => format!("/signup/advisor/{uuid}/"),
            WizardStep::Verify => format!("/signup/advisor/{uuid}/verify"),
            WizardStep::Terms => format!("/signup/advisor/{uuid}/terms"),
            WizardStep::Billing => format!("/signup/advisor/{uuid}/billing"),
            WizardStep::Password => format!("/signup/advisor/{uuid}/password"),
        }
    }

    fn enabled(&self, _step: &WizardStep, ctx: &Self::Context) -> bool {
        match self {
            // Index is always enabled
            WizardStep::Index => !ctx.done,
            WizardStep::Verify => ctx.step_contact_complete && !ctx.done,
            WizardStep::Terms => ctx.step_contact_complete && !ctx.done,
            WizardStep::Billing => {
                ctx.step_contact_complete
                    && ctx.step_verify_complete
                    && ctx.step_terms_complete
                    && !ctx.done
            }
            WizardStep::Password => {
                ctx.step_contact_complete
                    && ctx.step_verify_complete
                    && ctx.step_terms_complete
                    && ctx.step_billing_complete
                    && !ctx.done
            }
        }
    }

    fn complete(&self, _step: &WizardStep, ctx: &Self::Context) -> bool {
        match self {
            WizardStep::Index => ctx.step_contact_complete,
            WizardStep::Verify => ctx.step_verify_complete,
            WizardStep::Terms => ctx.step_terms_complete,
            WizardStep::Billing => ctx.step_billing_complete,
            WizardStep::Password => ctx.step_password_complete,
        }
    }

    fn error_message(&self, step: &WizardStep, ctx: &Self::Context) -> Option<String> {
        // Don't show errors on current or future steps
        if step <= self {
            return None;
        }

        match self {
            WizardStep::Index => ctx.step_contact_error.clone(),
            WizardStep::Verify => None,
            WizardStep::Terms => ctx.step_terms_error.clone(),
            WizardStep::Billing => ctx.step_billing_error.clone(),
            WizardStep::Password => ctx.step_password_error.clone(),
        }
    }
}
