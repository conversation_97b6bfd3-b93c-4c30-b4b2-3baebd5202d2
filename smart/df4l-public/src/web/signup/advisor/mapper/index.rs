#[approck::http(GET /signup/advisor/{signup_advisor_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        doc: Document,
        identity: Identity,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use crate::api::signup::advisor::{signup_advisor_contact_get, signup_advisor_wizard_data};
        use approck::html;

        let wizard_data = signup_advisor_wizard_data::call(
            app,
            identity,
            signup_advisor_wizard_data::Input {
                signup_advisor_uuid: path.signup_advisor_uuid,
            },
        )
        .await?;

        let personal_data = signup_advisor_contact_get::call(
            app,
            identity,
            signup_advisor_contact_get::Input {
                signup_advisor_uuid: path.signup_advisor_uuid,
            },
        )
        .await?;

        let mut wizard = bux::component::form_wizard::new(WizardStep::Index, wizard_data)?;
        wizard.set_id("personal-info-form");
        wizard.set_hidden("action", "signup");
        wizard.set_hidden("signup_advisor_uuid", path.signup_advisor_uuid);

        #[rustfmt::skip]
        wizard.add_body(html! {
            p {"Please fill out your personal details. All fields are required to continue."} br;
            grid-2 {
                (bux::input::text::string::name_label_value("first_name", "First Name:", personal_data.first_name.as_deref()))
                (bux::input::text::string::name_label_value("last_name", "Last Name:", personal_data.last_name.as_deref()))
                div {
                    (bux::input::text::string::name_label_value("phone", "Mobile Phone:", personal_data.phone.as_deref()))
                    p { "We'll send SMS updates to this mobile number." }
                }
                div {
                    (bux::input::text::string::name_label_value("email", "Primary Email:", personal_data.email.as_deref()))
                    p { "A verification code will be sent to this email address." }
                }
            }
        });

        doc.set_title("Personal Information");
        doc.hide_page_nav();
        doc.add_body(html! {
            (wizard)
        });

        Ok(Response::HTML(doc.into()))
    }
}
