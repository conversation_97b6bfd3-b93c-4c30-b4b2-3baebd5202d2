#terms-form {

    content {

        .terms {
            border: 1px solid #dee2e6;
            padding: 1.5rem;
            border-radius: 0.5rem;
            background-color: #fff;
            line-height: 1.6;
            font-size: 0.95rem;
            color: #333;
            p {
                margin-bottom: 1.25rem;
            }
            strong {
                font-weight: 600;
                color: #2c3e50;
            }
            em {
                color: #6c757d;
                font-style: italic;
            }
        }

        .x-sign {
            text-align: center;
            margin: 2rem auto;
            display: block;
            border-radius: 1rem;
            background-color: #fdffcf;
            border: 2px dashed #000;
            padding: 2rem;
            max-width: 500pt;

            input {
                font-size: 18pt;
                text-align: center;
            }

            .x-error:not(:empty) {
                background-color: #ffebee;
                color: #d32f2f;
                padding: 0.75rem;
                border: 1px solid #d32f2f;
                margin-bottom: 0.5rem;
                border-radius: 0.25rem;
            }
        }
    }
}
