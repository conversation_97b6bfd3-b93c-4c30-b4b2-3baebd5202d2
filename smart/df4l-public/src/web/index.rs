#[approck::http(GET /; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Debt2Capital™");

        doc.add_body(html!(
            div.bux-narrow-50 {
                img.df4l-logo src="https://asset7.net/Zagula/Smart/Debt2Capital/debt2capital_logo_trademark.svg" {}
                hr;
                p {
                    (bux::button::link::label_class("Dashboard", "/auth/", "primary"))
                }
            }
        ));

        Response::HTML(doc.into())
    }
}
