#[approck::http(GET /api/icover/client/{client_uuid:Uuid}/; AUTH None; return JSON|Text;)]
pub mod get {
    use crate::api::client::detail as client_api;

    pub async fn request(app: App, identity: Identity, path: Path) -> Result<Response> {
        // check permissions
        if !identity.scope_d2c_read() {
            let response = Text {
                status: approck::StatusCode::UNAUTHORIZED,
                content: "insufficient permissions to read client policy information".to_string(),
                ..Default::default()
            };
            return Ok(Response::Text(response));
        }

        let input = client_api::get::Input {
            client_uuid: path.client_uuid,
        };

        let output = client_api::get::call(app, identity.as_ref(), input).await?;

        // Client will not expect Some(value), etc.. so we're using serde instead
        Ok(Response::JSON(serde_json::json!(output).into()))
    }
}
