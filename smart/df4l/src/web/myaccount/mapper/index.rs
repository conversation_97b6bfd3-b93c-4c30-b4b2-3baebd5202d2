#[approck::http(GET /myaccount/{identity_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        doc.set_title("My Account");

        // Fetch all account data using our consolidated API
        use crate::api::myaccount::summary::get_myaccount_summary;

        let account_data = get_myaccount_summary::call(
            app,
            identity,
            get_myaccount_summary::Input {
                identity_uuid: path.identity_uuid,
            },
        )
        .await?;

        #[rustfmt::skip]
        // Prepare display values
        let display_email = account_data
            .email
            .as_deref()
            .unwrap_or("Email not available");

        let mut advisors = Vec::new();
        for advisor in account_data.advisors.iter() {
            let mut advisor_info = bux::component::insight_deck::InsightDeck::new("Advisor Info");
            advisor_info.description(
                "An overview of your advisor identifiers, contact details, and state licensures.",
            );
            advisor_info.add_css_class("advisor-info");
            advisor_info.add_basic_tile(
                "fas fa-id-badge",
                "Advisor ID",
                html!((advisor.advisor_esid)),
            );

            advisor_info.add_edit_tile(
                "fas fa-id-badge",
                "GBU Advisor ID",
                match &advisor.gbu_advisor_esid {
                    Some(id) => html!((id)),
                    None => html!("None"),
                },
                df4l_advisor::ml_myaccount_advisor_gbu(path.identity_uuid, advisor.advisor_uuid),
            );

            advisor_info.add_edit_row_phone(
                advisor.phone.as_deref(),
                format!(
                    "/myaccount/{}/advisor/{}/contact",
                    path.identity_uuid, advisor.advisor_uuid
                ),
            );
            advisor_info.add_edit_row_email(
                advisor.email.as_deref(),
                format!(
                    "/myaccount/{}/advisor/{}/contact",
                    path.identity_uuid, advisor.advisor_uuid
                ),
            );
            advisor_info.add_edit_row_address(
                advisor.address.as_deref(),
                format!(
                    "/myaccount/{}/advisor/{}/contact",
                    path.identity_uuid, advisor.advisor_uuid
                ),
            );

            // states of licensure
            advisor_info.add_edit_row(
                "fas fa-id-badge",
                "States of Licensure",
                html! {
                    @if advisor.states_of_licensure.is_empty() {
                        label-tag.danger { "None" }
                    } @else {
                        @for (i, state) in advisor.states_of_licensure.iter().enumerate() {
                            @if i > 0 {
                                " "
                            }
                            label-tag.primary { (state) }
                        }
                    }
                },
                df4l_advisor::ml_myaccount_advisor_statelic(
                    path.identity_uuid,
                    advisor.advisor_uuid,
                ),
            );

            advisors.push(advisor_info);
        }

        // Create Login Info InsightDeck
        let mut login_info = bux::component::insight_deck::InsightDeck::new("Login Info");
        login_info.description("A summary of your login credentials and recent login activity.");
        login_info.add_css_class("login-info");

        // Add Details button
        login_info.add_button(bux::button::link::label_icon_class(
            "Details",
            "fas fa-arrow-right",
            &format!("/myaccount/{}/security/", path.identity_uuid),
            "sm primary",
        ));

        // Add login info tiles
        login_info.add_basic_tile(
            "fas fa-fingerprint",
            "Username",
            html!((account_data.username)),
        );

        let password_last_changed = account_data
            .password_last_changed
            .as_deref()
            .unwrap_or("Unknown");
        login_info.add_basic_tile(
            "fas fa-key",
            "Password last updated",
            html!((password_last_changed)),
        );

        let last_login = account_data.last_login.as_deref().unwrap_or("Never");
        login_info.add_basic_tile("fas fa-sign-in-alt", "Last login", html!((last_login)));

        let login_attempts = if account_data.login_attempts_today == 0 {
            "0 attempts".to_string()
        } else {
            format!("{} attempts", account_data.login_attempts_today)
        };
        login_info.add_basic_tile(
            "fas fa-calendar-alt",
            "Login attempts today",
            html!((login_attempts)),
        );

        // Create Security Settings InsightDeck
        let mut security_settings =
            bux::component::insight_deck::InsightDeck::new("Security Settings");
        security_settings
            .description("An overview of your current security settings and verification status");
        security_settings.add_css_class("security-settings");

        // Add Details button
        security_settings.add_button(bux::button::link::label_icon_class(
            "Details",
            "fas fa-arrow-right",
            &format!("/myaccount/{}/security/", path.identity_uuid),
            "sm primary",
        ));

        // Add security settings rows
        security_settings.add_basic_row(
            "fas fa-shield-alt",
            "2-Step Verification",
            html!(label-tag.success { "Enabled" }),
        );

        let mfa_email_status = if account_data.mfa_email_enabled {
            html!(label-tag.success { "MFA Email Enabled" })
        } else {
            html!(label-tag.warning { "MFA Email Disabled" })
        };
        security_settings.add_basic_row(
            "fas fa-envelope-open-text",
            "MFA By Email",
            mfa_email_status,
        );

        security_settings.add_basic_row(
            "fas fa-cog",
            "MFA By Authenticator App",
            html!(label-tag.success { "Authenticator App Enabled" }),
        );

        security_settings.add_basic_row(
            "fab fa-google",
            "Google Login",
            html!(label-tag.success { "Enabled" }),
        );

        security_settings.add_basic_row(
            "fas fa-mobile-alt",
            "Mobile Number",
            html!(label-tag.success { "Verified" }),
        );

        security_settings.add_basic_row(
            "fas fa-envelope-open-text",
            "Email Address",
            html!(label-tag.success { "Verified" }),
        );

        doc.add_body(html!(
            insight-deck {
                grid-12 {
                    cell-3 {
                        panel {
                            content {
                                contact-info {
                                    h1 { (account_data.name) }
                                    p.phone.mb-0 {
                                        "Phone not available"
                                    }
                                    p.email {
                                        @if let Some(email) = &account_data.email {
                                            a href=(format!("mailto:{}", email)) { (email) }
                                        } @else {
                                            (display_email)
                                        }
                                    }
                                    hr;
                                    a.btn.primary.block href="#" { "Visit Stripe Billing" }
                                }
                            }
                        }
                    }
                    cell-9 {
                        // Login Info using InsightDeck component
                        (login_info)

                        // Security Settings using InsightDeck component
                        (security_settings)

                        // Advisors using InsightDeck component
                        @for advisor in &advisors {
                            (advisor)
                        }
                    }
                }
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}
