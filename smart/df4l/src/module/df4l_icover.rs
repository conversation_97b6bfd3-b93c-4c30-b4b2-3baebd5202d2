impl df4l_icover::App for crate::AppStruct {
    fn auth_fence_provider(&self) -> &auth_fence_provider::ModuleStruct {
        &self.auth_fence_provider
    }
    fn take_me_to_icover_client_url(&self, client_uuid: granite::Uuid) -> String {
        format!(
            "https://gbu-local-development.icoverdemo.com/?client_uuid={}",
            client_uuid
        )
    }
}

impl df4l_icover::Identity for crate::IdentityStruct {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }
    fn scope_d2c_read(&self) -> bool {
        // TODO:DF4l: SECON REVIEW - this method needs a client_uuid
        if self.df4l_advisor.is_some() {
            return true;
        }

        match &self.auth_fence_provider {
            Some(auth_fence_provider) => auth_fence_provider.scope_d2c_read,
            None => false,
        }
    }
}
