#[approck::api]
pub mod client_debt_free_info {
    use granite::return_authorization_error;

    use chrono::{Datelike, Local, NaiveDate};
    //use rust_decimal::Decimal;
    use bux::format_currency_us_0;
    use granite::Decimal;
    use std::collections::HashMap;
    use std::str::FromStr;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub advisor_uuid: Uuid,
        pub generate_plan: bool,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Client {
        pub client_uuid: Uuid,
        pub name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub phone2: Option<String>,
        pub monthly_budget: Option<Decimal>,
        pub annual_insurance_premium: Option<Decimal>,
        pub annual_insurance_base: Option<Decimal>,
        pub annual_insurance_pua: Option<Decimal>,
        pub net_cash_at_end: Option<Decimal>,
        pub debt_free_start_date: Option<DateUtc>,
        pub debt_free_extra_pua_list: Vec<ExmExa>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Advisor {
        pub advisor_uuid: Uuid,
        pub first_name: String,
        pub last_name: String,
        pub name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct ClientDebtItem {
        pub client_debt_uuid: Uuid,
        pub name: String,
        pub balance: Option<Decimal>,
        pub interest_rate: Option<Decimal>,
        pub monthly_payment: Option<Decimal>,
        pub active: bool,
        pub note: Option<String>,
    }

    // ABID: VERIFIED
    #[granite::gtype(ApiOutput)]
    pub struct DebtInfo {
        pub start_year: u32,
        pub start_month: u32,

        pub debt_count: Option<i32>,
        pub debt_balance: Option<Decimal>,
        pub monthly_budget: Option<Decimal>,
        pub annual_budget: Option<Decimal>,
        pub minimum_monthly_payment: Option<Decimal>,
        pub average_interest_rate: Option<Decimal>,
        pub total_monthly_interest: Option<Decimal>,
        pub total_monthly_interest_percent: Option<Decimal>,

        pub annual_insurance_premium: Option<Decimal>,
        pub annual_insurance_pua: Option<Decimal>,
        pub monthly_insurance_premium: Option<Decimal>,
        pub monthly_insurance_pua: Option<Decimal>,

        pub annual_insurance_base: Option<Decimal>,
        pub monthly_insurance_base: Option<Decimal>,

        pub extra_pua_map: HashMap<u32, Decimal>,

        pub net_cash_at_end: Option<Decimal>,
        pub percent_to_paid_up_additions: Option<Decimal>,

        pub upcoming_changes: UpcomingChanges,
        pub debt_list: Vec<DebtInfoDebtItem>,
        //pub error_list: Vec<String>, // TODO: remove this, it's used in the legacy system
        pub plan: Vec<MonthPlan>,

        pub total_paid: Decimal,
        pub total_unpaid: Decimal,
        pub esimated_cash_value: Decimal,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client: Client,
        pub debt_info: DebtInfo,
    }

    // ABID: VERIFIED
    #[granite::gtype(ApiOutput)]
    pub struct DebtInfoDebtItem {
        pub client_debt_uuid: Uuid,
        pub debt_name: String,
        pub full_name: String,
        pub balance: Decimal,
        pub interest_rate: Decimal,
        pub monthly_payment: Decimal,
        pub monthly_interest_percent: Option<Decimal>,
        pub monthly_interest: Option<Decimal>,
        pub active: bool,

        pub payoff_month: Option<u32>,
        pub payoff_date: Option<String>,
        pub payoff_date_formatted: Option<String>, // '23, May, NEW
        pub payoff_interest: Decimal,
        pub standard_payoff_month: u32,
        pub standard_payoff_interest: Decimal,

        // NEW FIELDS ADDED TO POWER RUST UI
        pub effective_interest_cost_percent: Decimal,
        pub is_paid_off_as_of_today: bool,
    }

    // NEW STRUCTURE. No verification beeded
    #[granite::gtype(ApiOutput)]
    pub struct UpcomingChanges {
        pub current_month: Option<MonthChange>,
        pub next_month: Option<MonthChange>,
        pub following_month: Option<MonthChange>,
    }

    //ABID: VERIFIED
    #[granite::gtype(ApiOutput)]
    pub struct MonthChange {
        pub starting_balance: Decimal,
        pub ending_balance: Decimal,
        pub interest_charge: Decimal,
        pub minimum_payment: Decimal,
        pub this_month_extra: Vec<String>, // SMS: Snowball Shift. last_moth_extra, this_month_extra, this_month_total_plus_extra are used for displaying the message, not for calculations.
        pub payoff: Vec<String>,
    }

    // NEW STRUCTURE. No verification beeded
    #[derive(Clone)]
    #[granite::gtype(ApiOutput)]
    pub struct MonthYear {
        pub month: u32,
        pub year: u32,
    }

    // ABID: STRUCTURE VERIED
    #[derive(Clone)]
    #[granite::gtype(ApiOutput)]
    pub struct MonthPlan {
        pub month: u32,
        pub month_text: String,
        pub month_year_tuple: MonthYear, // TODO: rename this to month_year once conversion is complete and verified
        pub debt_list: Vec<MonthDebt>,   // TOOD: VERIFY STRUCTURE
        pub premium_total: Decimal,
        pub premium_pua: Decimal,
        pub premium_base: Decimal,

        pub pua_added: Decimal,
        pub pua_extra: Decimal,
        pub pua_balance: Decimal,

        pub loan_starting_balance: Decimal,
        pub loan_ending_balance: Decimal,
        pub loan_draw: Decimal,
        pub loan_paid: Decimal,

        pub budget_total: Decimal,
        pub budget_premium: Decimal,
        pub budget_minimum_payments: Decimal,
        pub budget_extra_payments: Decimal,
        pub budget_repayment: Decimal,
        pub budget_remaining: Decimal,

        pub debt_starting_balance: Decimal,
        pub debt_interest_charge: Decimal,
        pub debt_paid_down: Decimal,
        pub debt_ending_balance: Decimal,

        pub cash_value: Decimal,
        pub cash_net: Decimal,

        pub sms_map: HashMap<String, String>,
    }

    // ABID: STRUCTURE VERIFIED
    // This debt is used by MonthPlan
    #[derive(Clone)]
    #[granite::gtype(ApiOutput)]
    pub struct MonthDebt {
        pub full_name: String,
        pub starting_balance: Decimal,
        pub interest_charge: Decimal,
        pub minimum_payment: Decimal,
        pub extra_payment: Decimal,
        pub payoff_payment: Decimal,
        pub ending_balance: Decimal,
    }
    // [{"Month": 1, "Amount": "8000"}]
    #[granite::gtype(ApiOutput)]
    #[derive(serde::Deserialize)]
    pub struct ExmExa {
        pub month: u32,
        pub amount: Decimal,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .df4l_zero_client_read(&dbcx, input.client_uuid)
            .await
        {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
                $client_uuid: &input.client_uuid,
            };
            row = {
                client_uuid: Uuid,
                advisor_uuid: Option<Uuid>,
                name: String,
                email: Option<String>,
                phone: Option<String>,
                phone2: Option<String>,
                monthly_budget: Option<Decimal>,
                annual_insurance_premium: Option<Decimal>,
                annual_insurance_pua: Option<Decimal>,
                net_cash_at_end: Option<Decimal>,
                debt_free_start_date: Option<DateUtc>,
                debt_free_extra_pua_list: String,
            };
            SELECT
                client_uuid,
                advisor_uuid,
                first_name || " " || last_name AS name,
                email,
                phone,
                phone2,
                monthly_budget,
                annual_insurance_premium,
                annual_insurance_pua,
                net_cash_at_end,
                debt_free_start_date,
                debt_free_extra_pua_list::text AS debt_free_extra_pua_list
            FROM
                df4l.client0
            WHERE true
            AND advisor_uuid = $advisor_uuid::uuid
            AND client_uuid = $client_uuid::uuid
        )
        .await?;

        // Parse debt_free_extra_pua_list from JSON string to Vec<ExmExa>
        let debt_free_extra_pua_list: Vec<ExmExa> =
            match serde_json::from_str(&row.debt_free_extra_pua_list) {
                Ok(debt_free_extra_pua_list) => debt_free_extra_pua_list,
                Err(e) => {
                    return Err(granite::Error::new(granite::ErrorType::Unexpected)
                        .set_external_message(format!(
                            "Could not parse debt_free_extra_pua_list: {}",
                            e
                        )));
                }
            };

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
            };
            row = {
                client_debt_uuid: Uuid,
                name: String,
                balance: Option<Decimal>,
                interest_rate: Option<Decimal>,
                monthly_payment: Option<Decimal>,
                active: bool,
                note: Option<String>,
            };
            SELECT
                cd.client_debt_uuid,
                cd.name,
                cd.balance,
                cd.interest_rate,
                cd.monthly_payment,
                cd.active,
                cd.note
            FROM
                df4l.client0_debt AS cd
            WHERE
                cd.client_uuid = $client_uuid::uuid
            ORDER BY
                balance, client_debt_uuid  --smallest first, deterministic
        )
        .await?;

        // Create Client struct
        let client = Client {
            client_uuid: row.client_uuid,
            name: row.name,
            email: row.email,
            phone: row.phone,
            phone2: row.phone2,
            monthly_budget: row.monthly_budget,
            net_cash_at_end: row.net_cash_at_end,
            annual_insurance_premium: row.annual_insurance_premium,
            annual_insurance_pua: row.annual_insurance_pua,
            annual_insurance_base: if let (Some(premium), Some(pua)) =
                (row.annual_insurance_premium, row.annual_insurance_pua)
            {
                Some(premium - pua)
            } else {
                None
            },
            debt_free_start_date: row.debt_free_start_date,
            debt_free_extra_pua_list,
        };

        // Create ClientDebtInfo struct
        let debt_list: Vec<ClientDebtItem> = rows
            .into_iter()
            .map(|r| ClientDebtItem {
                client_debt_uuid: r.client_debt_uuid,
                name: r.name,
                balance: r.balance,
                interest_rate: r.interest_rate,
                monthly_payment: r.monthly_payment,
                active: r.active,
                note: r.note,
            })
            .collect();

        // Create DebtInfo struct
        ////////////////////////////////////////////////////////////////////////////
        // loop through client_report.debt_list and initialize debt_items

        let mut debt_items: Vec<DebtInfoDebtItem> = vec![];
        let mut error_list = Vec::new();
        for client0_debt in debt_list {
            if client0_debt.balance.is_none() {
                error_list.push(format!("Debt {} has no balance", client0_debt.name));
                continue;
            }
            if client0_debt.interest_rate.is_none() {
                error_list.push(format!("Debt {} has no interest rate", client0_debt.name));
                continue;
            }
            /*
            def IsPaymentEnough(self):
            if self.MonthlyPayment is None or self.InterestRate is None or self.Balance is None:
              return None
            return self.MonthlyPayment > (self.Balance * self.InterestRate * Decimal('0.01') / 12)


            if not debt.IsPaymentEnough:
                RVAL.EL.Add(f'Monthly payment on {debt.FullName} is not enough to cover monthly interest charges.', Key=debt.AdvisorClient_Debt_ZNID)
            */
            fn is_payment_enough(
                monthly_payment: &Option<Decimal>,
                interest_rate: &Option<Decimal>,
                balance: &Option<Decimal>,
            ) -> Option<bool> {
                let monthly_payment = match monthly_payment {
                    Some(v) => v,
                    None => return None,
                };
                let interest_rate = match interest_rate {
                    Some(v) => v,
                    None => return None,
                };
                let balance = match balance {
                    Some(v) => v,
                    None => return None,
                };
                Some(
                    monthly_payment
                        > &(balance * interest_rate * Decimal::from_str("0.01").unwrap()
                            / Decimal::from(12)),
                )
            }

            if let Some(false) = is_payment_enough(
                &client0_debt.monthly_payment,
                &client0_debt.interest_rate,
                &client0_debt.balance,
            ) {
                error_list.push(format!(
                    "Monthly payment on {} is not enough to cover monthly interest charges.",
                    client0_debt.name
                ));
                continue;
            }

            /*
            def MonthlyInterest(self):
                if not self.Balance or not self.MonthlyPayment or not self.InterestRate:
                    return None
                return round((float(self.Balance) * (float(self.InterestRate)*0.01) / 12),2)
            */
            fn monthly_interest(
                balance: &Option<Decimal>,
                interest_rate: &Option<Decimal>,
                monthly_payment: &Option<Decimal>,
            ) -> Option<Decimal> {
                let balance = match balance {
                    Some(v) => v,
                    None => return None,
                };
                let interest_rate = match interest_rate {
                    Some(v) => v,
                    None => return None,
                };
                let _monthly_payment = match monthly_payment {
                    Some(v) => v,
                    None => return None,
                };
                Some(
                    (balance * interest_rate * Decimal::from_str("0.01").unwrap()
                        / Decimal::from(12))
                    .round_dp(2),
                )
            }

            debt_items.push(DebtInfoDebtItem {
                client_debt_uuid: client0_debt.client_debt_uuid,
                debt_name: client0_debt.name.clone(),
                full_name: client0_debt.name,
                balance: client0_debt.balance.unwrap_or(Decimal::ZERO),
                interest_rate: client0_debt.interest_rate.unwrap_or(Decimal::ZERO),
                monthly_payment: client0_debt.monthly_payment.unwrap_or(Decimal::ZERO),
                monthly_interest: monthly_interest(
                    // NOTE: legacy system is using monthly_interest DB value. New system doesn'y have it.
                    &client0_debt.balance,
                    &client0_debt.interest_rate,
                    &client0_debt.monthly_payment,
                ),
                monthly_interest_percent: None, // TODO: Check it. legacy system used MonthlyInterestPercent value from the DB.
                active: client0_debt.active,
                payoff_month: None,
                payoff_date: None,
                payoff_date_formatted: None,
                payoff_interest: Decimal::ZERO,
                standard_payoff_month: 0,
                standard_payoff_interest: Decimal::ZERO,

                // NEW FIELDS ADDED TO POWER RUST UI
                effective_interest_cost_percent: Decimal::ZERO,
                is_paid_off_as_of_today: false,
            });
        }

        // Return error response if no debts are provided
        //if debt_list.is_empty() {
        if true {
            error_list.push("You must have at least one debt entered.".to_string());
        }

        /*
        if !error_list.is_empty() {
            return Self {
                error_list,
                ..Default::default()
            };
        }
        */

        // Set calculated data
        let today = Local::now().date_naive();

        // Program start date. It is used to calculate monthly offsets
        let start_date = client.debt_free_start_date;
        let start_year = start_date.map_or(today.year(), |d| d.year()) as u32;
        let start_month = start_date.map_or(today.month(), |d| d.month());

        // Set monthly budget and insurance values
        let monthly_budget = client.monthly_budget; //Some(Decimal::new(2000, 0));
        let annual_insurance_premium = client.annual_insurance_premium; // Some(Decimal::new(6000, 0));
        let annual_insurance_pua = client.annual_insurance_pua; // Some(Decimal::new(2000, 0));

        let mut info = DebtInfo {
            start_year,  // VERIFIED
            start_month, // VERIFIED

            debt_count: Some(debt_items.len() as i32), // VERIFIED
            debt_balance: Some(debt_items.iter().map(|d| d.balance).sum()), // VERIFIED
            monthly_budget,                            // VERIFIED
            annual_budget: monthly_budget.map(|mb| mb * Decimal::new(12, 0)), // VERIFIED

            minimum_monthly_payment: Some(debt_items.iter().map(|d| d.monthly_payment).sum()), // VERIFIED
            average_interest_rate: None,  // CHECK IT BELOW - VERIFIED
            total_monthly_interest: None, // CHECK IT BELOW - VERIFIED
            total_monthly_interest_percent: None, // CHECK IT BELOW - VERIFIED

            annual_insurance_premium, // VERIFIED
            annual_insurance_pua,     // VERIFIED
            monthly_insurance_premium: annual_insurance_premium.map(|p| p / Decimal::new(12, 0)), // VERIFIED
            monthly_insurance_pua: annual_insurance_pua.map(|p| p / Decimal::new(12, 0)), // VERIFIED

            annual_insurance_base: None,  // CHECK IT BELOW - VERIFIED
            monthly_insurance_base: None, // CHECK IT BELOW - VERIFIED

            percent_to_paid_up_additions: None, // CHECK IT BELOW - VERIFIED

            extra_pua_map: HashMap::new(), // CHECK IT BELOW - TODO - IMPLEMENT

            net_cash_at_end: client.net_cash_at_end, // VERIFIED

            total_paid: Decimal::ZERO,   // NEW FIELD TO POWER RUST SCREEN
            total_unpaid: Decimal::ZERO, // NEW FIELD TO POWER RUST SCREEN
            esimated_cash_value: Decimal::ZERO, // NEW FIELD TO POWER RUST SCREEN

            upcoming_changes: UpcomingChanges {
                current_month: None,
                next_month: None,
                following_month: None,
            },
            debt_list: debt_items,
            plan: Vec::new(),
        };

        // Calculate insurance base values
        if let (Some(premium), Some(pua)) =
            (info.annual_insurance_premium, info.annual_insurance_pua)
        {
            info.annual_insurance_base = Some(premium - pua);
        }

        if let (Some(premium), Some(pua)) =
            (info.monthly_insurance_premium, info.monthly_insurance_pua)
        {
            info.monthly_insurance_base = Some(premium - pua);
        }

        // Calculate average interest rate
        if let Some(debt_balance) = info.debt_balance {
            if debt_balance > Decimal::ZERO {
                let weighted_sum: Decimal = info
                    .debt_list
                    .iter()
                    .map(|d| d.interest_rate * d.balance)
                    .sum();
                info.average_interest_rate =
                    Some((weighted_sum / debt_balance).round() / Decimal::new(100, 0));
            }
        }

        // Calculate monthly interest totals
        let total_monthly_interest = info
            .debt_list
            .iter()
            .filter_map(|d| d.monthly_interest)
            .sum::<Decimal>();

        info.total_monthly_interest = Some(total_monthly_interest);

        if let Some(min_payment) = info.minimum_monthly_payment {
            if min_payment > Decimal::ZERO {
                info.total_monthly_interest_percent = Some(
                    ((total_monthly_interest / min_payment) * Decimal::new(100, 0)).round_dp(2),
                );
            }
        }

        // Calculate percent to paid up additions
        info.percent_to_paid_up_additions = Some(
            (info.monthly_insurance_pua.unwrap_or(Decimal::ZERO)
                / info.monthly_budget.unwrap_or(Decimal::ZERO))
                * Decimal::new(100, 0),
        );

        // Calculate extra_pua_map. it's fetched from client.debt_free_extra_pua_list
        for row in &client.debt_free_extra_pua_list {
            let amount = info
                .extra_pua_map
                .get(&row.month)
                .cloned()
                .unwrap_or(Decimal::ZERO);
            info.extra_pua_map.insert(row.month, amount + row.amount);
        }

        // /////////////////////////////////////////////////////////////////////////////////////////////
        // Load advisor info
        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
            };
            row = {
                advisor_uuid: Uuid,
                first_name: String,
                last_name: String,
                name: String,
                email: Option<String>,
                phone: Option<String>,
            };
            SELECT
                advisor_uuid,
                first_name,
                last_name,
                first_name || " " || last_name AS name,
                email,
                phone
            FROM
                df4l.advisor
            WHERE
                advisor_uuid = $advisor_uuid::uuid
        )
        .await?;
        let advisor = Advisor {
            advisor_uuid: row.advisor_uuid,
            first_name: row.first_name,
            last_name: row.last_name,
            name: row.name,
            email: row.email,
            phone: row.phone,
        };

        // /////////////////////////////////////////////////////////////////////////////////////////////
        // Calculate plan

        generate_plan(&client, &advisor, &mut info, Some(today)).unwrap();

        // EOF Calculate plan
        // /////////////////////////////////////////////////////////////////////////////////////////////

        // Calculate total paid and total unpaid amounts
        info.total_paid = info
            .plan
            .iter()
            .map(|m| m.premium_total + m.budget_total)
            .sum();

        info.total_unpaid = info.debt_balance.unwrap_or_default() - info.total_paid;

        Ok(Output {
            client,
            debt_info: info,
        })
    }

    // Format decimal optional value as $100/mo $1,200/yr or $100.50/mo $1,200.50/yr if cents exist
    pub fn format_decimal_optional(value: Option<Decimal>, empty_value: &str) -> String {
        if let Some(value) = value {
            let monthly = value / Decimal::from(12);
            let yearly = value;

            // Check if monthly has cents
            let monthly_str = if monthly.fract().is_zero() {
                format!("{}/mo", format_currency_us_0(monthly.trunc()))
            } else {
                format!("{}/mo", format_currency_us_0(monthly))
            };

            // Check if yearly has cents
            let yearly_str = if yearly.fract().is_zero() {
                format!("{}/yr", format_currency_us_0(yearly.trunc()))
            } else {
                format!("{}/yr", format_currency_us_0(yearly))
            };

            format!("{} {}", monthly_str, yearly_str)
        } else {
            empty_value.to_string()
        }
    }

    // Return date in the format: '25 Jun
    pub fn date_formatted_yy_mon(year: u32, month: u32) -> String {
        format!(
            "'{} {}",
            year % 100,
            month_number_to_abbr(month).unwrap_or("")
        )
    }

    pub fn month_number_to_abbr(month: u32) -> Option<&'static str> {
        match month {
            1 => Some("Jan"),
            2 => Some("Feb"),
            3 => Some("Mar"),
            4 => Some("Apr"),
            5 => Some("May"),
            6 => Some("Jun"),
            7 => Some("Jul"),
            8 => Some("Aug"),
            9 => Some("Sep"),
            10 => Some("Oct"),
            11 => Some("Nov"),
            12 => Some("Dec"),
            _ => None,
        }
    }

    // Helper function to get date tuple
    fn get_date_tuple(months: u32, start_month: u32, start_year: u32) -> (u32, u32) {
        let mut new_month = start_month + months;
        // Note, we are using integer division here, which truncates towards zero
        let mut new_year = start_year + new_month / 12;
        new_month %= 12; //new_month = new_month % 12;
        if new_month == 0 {
            new_year -= 1;
            new_month = 12; // Set to 12 if the result was 0
        }
        (new_month, new_year)
    }

    // Helper function to add months to a date
    fn add_months(date: NaiveDate, months: u32) -> NaiveDate {
        let mut year = date.year() as u32;
        let mut month = date.month() + months;

        while month > 12 {
            month -= 12;
            year += 1;
        }

        while month < 1 {
            month += 12;
            year -= 1;
        }

        // Create new date with same day (or last day of month if day is invalid)
        let last_day = get_last_day_of_month(year, month);
        let day = std::cmp::min(date.day(), last_day);

        NaiveDate::from_ymd_opt(year as i32, month, day).unwrap_or(date)
    }

    // Helper function to get the last day of a month
    fn get_last_day_of_month(year: u32, month: u32) -> u32 {
        match month {
            1 | 3 | 5 | 7 | 8 | 10 | 12 => 31,
            4 | 6 | 9 | 11 => 30,
            2 => {
                if is_leap_year(year) {
                    29
                } else {
                    28
                }
            }
            _ => 30, // Default for invalid months
        }
    }

    // Helper function to check if a year is a leap year
    fn is_leap_year(year: u32) -> bool {
        (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)
    }

    // TODO: VERIFY generate_plan with Smart python code
    fn generate_plan(
        client: &Client,
        advisor: &Advisor,
        debt_info: &mut DebtInfo,
        run_date: Option<NaiveDate>,
    ) -> Result<(), String> {
        if debt_info.debt_list.is_empty() {
            return Err("You must have at least one debt entered.".to_string());
        }

        if debt_info.monthly_insurance_premium.is_none() {
            return Err("Please add the Annual Insurance Premium.".to_string());
        }

        if debt_info.monthly_insurance_pua.is_none() {
            return Err("Please add the Annual Insurance PUA.".to_string());
        }

        // Month name mapping
        let ym: HashMap<u32, &str> = [
            (1, "Jan"),
            (2, "Feb"),
            (3, "Mar"),
            (4, "Apr"),
            (5, "May"),
            (6, "Jun"),
            (7, "Jul"),
            (8, "Aug"),
            (9, "Sep"),
            (10, "Oct"),
            (11, "Nov"),
            (12, "Dec"),
        ]
        .iter()
        .cloned()
        .collect();

        // Helper function to get date string
        let get_date = |months: u32| -> String {
            let (new_month, new_year) =
                get_date_tuple(months, debt_info.start_month, debt_info.start_year);
            format!("{}\u{2011}{}", ym[&new_month], new_year) // Non-breaking hyphen. NOTE, legacy system used chr(8209) instead of Non-breaking hyphen
        };

        // Generate month-by-month plan (simplified)
        debt_info.plan = Vec::new();
        let all_paid = false;
        let mut last_month_plan: Option<MonthPlan> = None;

        for i in 1..361 {
            if all_paid {
                break;
            }

            // calculate month start date considering program start date stored in debt_info
            let (month, year) = get_date_tuple(i, debt_info.start_month, debt_info.start_year);
            //let month_start_date = NaiveDate::from_ymd_opt(year as i32, month, 1).unwrap();

            let mut month_plan = MonthPlan {
                month: i,                                    // ABID: VERIFIED
                month_text: get_date(i),                     // ABID: VERIFIED
                month_year_tuple: MonthYear { month, year }, // ABID: VERIFIED
                debt_list: Vec::new(),                       // ABID: VERIFIED - SET BELOW
                premium_total: Decimal::ZERO,                // ABID: VERIFIED - SET BELOW
                premium_pua: Decimal::ZERO,                  // ABID: VERIFIED - SET BELOW
                premium_base: Decimal::ZERO,                 // ABID: VERIFIED - SET BELOW

                debt_starting_balance: Decimal::ZERO,
                debt_ending_balance: Decimal::ZERO,
                loan_starting_balance: Decimal::ZERO,
                loan_ending_balance: Decimal::ZERO,
                loan_draw: Decimal::ZERO,
                loan_paid: Decimal::ZERO,

                budget_total: Decimal::ZERO, // ABID: VERIFIED - SET BELOW
                budget_premium: Decimal::ZERO, // ABID: VERIFIED - SET BELOW
                budget_minimum_payments: Decimal::ZERO, // ABID: VERIFIED - SET BELOW
                budget_extra_payments: Decimal::ZERO, // ABID: VERIFIED - SET BELOW
                budget_repayment: Decimal::ZERO, // ABID: VERIFIED - SET BELOW
                budget_remaining: Decimal::ZERO, // ABID: VERIFIED - SET BELOW

                pua_added: Decimal::ZERO,   // ABID: VERIFIED - SET BELOW
                pua_extra: Decimal::ZERO,   // ABID: VERIFIED - SET BELOW
                pua_balance: Decimal::ZERO, // ABID: VERIFIED - SET BELOW

                debt_interest_charge: Decimal::ZERO,
                debt_paid_down: Decimal::ZERO,
                cash_value: Decimal::ZERO,
                cash_net: Decimal::ZERO,
                sms_map: HashMap::new(),
            };

            // Initialize debt list for this month
            // Month.DebtList = [aadict(FullName=d.FullName) for d in RVAL.DebtList] # [{FullName: 'Credit Card 1', }]
            month_plan.debt_list = debt_info
                .debt_list
                .iter()
                .map(|d| MonthDebt {
                    full_name: d.full_name.clone(),
                    starting_balance: Decimal::ZERO,
                    interest_charge: Decimal::ZERO,
                    minimum_payment: Decimal::ZERO,
                    extra_payment: Decimal::ZERO,
                    payoff_payment: Decimal::ZERO,
                    ending_balance: Decimal::ZERO,
                })
                .collect();

            // Set premium values - VERIFIED
            month_plan.premium_total = debt_info.monthly_insurance_premium.unwrap_or(Decimal::ZERO);
            month_plan.premium_pua = debt_info.monthly_insurance_pua.unwrap_or(Decimal::ZERO);
            month_plan.premium_base = month_plan.premium_total - month_plan.premium_pua;

            // PUA values - VERIFIED
            //Month.PUA_Added = round(Month.Premium_PUA * Decimal('0.8464'),2)  #magical Zagula number
            //Month.PUA_Extra = RVAL.ExtraPUAMap.get(i, 0)
            //Month.PUA_Balance = (LastMonth.PUA_Balance if LastMonth else 0) + Month.PUA_Added + Month.PUA_Extra
            month_plan.pua_added = month_plan.premium_pua * Decimal::new(8464, 4);
            month_plan.pua_extra = debt_info
                .extra_pua_map
                .get(&i)
                .cloned()
                .unwrap_or(Decimal::ZERO);
            month_plan.pua_balance = if let Some(ref last) = last_month_plan {
                // ABID: used ref to not move the value
                last.pua_balance + month_plan.pua_added + month_plan.pua_extra
            } else {
                month_plan.pua_added + month_plan.pua_extra
            };

            // Start with full budget
            //remaining_budget = RVAL.MonthlyBudget
            let mut remaining_budget = debt_info.monthly_budget.unwrap_or(Decimal::ZERO);

            //First pay premium
            //remaining_budget -= Month.Premium_Total
            remaining_budget -= month_plan.premium_total;

            //loan_used = 0
            let mut loan_used = Decimal::ZERO;

            //remaining_cash_balance = LastMonth.Cash_Value if LastMonth else 0
            let mut remaining_cash_balance = if let Some(ref last) = last_month_plan {
                last.cash_value
            } else {
                Decimal::ZERO
            };

            // ZIP replacement
            //Debt_MonthDebt_LastMonthDebt_List = list(zip(RVAL.DebtList, Month.DebtList, LastMonth.DebtList if LastMonth else [None for d in RVAL.DebtList]))
            // Iterate over the zipped vectors.
            // `d1.iter_mut()` provides mutable references to elements of d1,
            // allowing us to modify them.
            // `d2.iter()` and `d3.iter()` provide immutable references, as they are not modified.
            // The `zip` method stops when the shortest iterator is exhausted,
            // mimicking Python's `zip` behavior.

            /*
            if let Some(last) = last_month_plan {
                let debt_monthdebt_lastmonthdebt_list: Vec<(&DebtInfoDebtItem, &mut MonthDebt, Option<&MonthDebt>)> =
                    debt_info.debt_list.iter().zip(month_plan.debt_list.iter_mut()).zip(last.debt_list.iter()).collect();
            } else {
                let debt_monthdebt_lastmonthdebt_list: Vec<(&DebtInfoDebtItem, &mut MonthDebt, Option<&MonthDebt>)> =
                    debt_info.debt_list.iter().zip(month_plan.debt_list.iter_mut()).zip(std::iter::repeat(None)).collect();
            }
            */

            let mut debt_monthdebt_lastmonthdebt_list: Vec<(
                &mut DebtInfoDebtItem,
                &mut MonthDebt,
                Option<&MonthDebt>,
            )>;

            if let Some(ref last) = last_month_plan {
                debt_monthdebt_lastmonthdebt_list = debt_info
                    .debt_list
                    .iter_mut()
                    .zip(month_plan.debt_list.iter_mut())
                    .zip(last.debt_list.iter())
                    .map(|((debt_item, month_debt), last_month_debt)| {
                        (debt_item, month_debt, Some(last_month_debt))
                    })
                    .collect();
            } else {
                debt_monthdebt_lastmonthdebt_list = debt_info
                    .debt_list
                    .iter_mut()
                    .zip(month_plan.debt_list.iter_mut())
                    .zip(std::iter::repeat(None))
                    .map(|((debt_item, month_debt), last_month_debt_option)| {
                        (debt_item, month_debt, last_month_debt_option)
                    })
                    .collect();
            }

            // # Second, pay all minimum payments
            /*
            for Debt, MonthDebt, LastMonthDebt in Debt_MonthDebt_LastMonthDebt_List:
                MonthDebt.StartingBalance = LastMonthDebt.EndingBalance if LastMonthDebt else Debt.Balance
                MonthDebt.InterestCharge = round(MonthDebt.StartingBalance * Debt.InterestRate * Decimal('0.01') / 12, 2)
                Debt.PayoffInterest += MonthDebt.InterestCharge
                MonthDebt.MinimumPayment = min(Debt.MonthlyPayment, MonthDebt.StartingBalance + MonthDebt.InterestCharge)
                MonthDebt.ExtraPayment = None
                MonthDebt.PayoffPayment = None
                MonthDebt.EndingBalance = None

                remaining_budget -= MonthDebt.MinimumPayment
             */
            for (debt, month_debt, last_month_debt) in &mut debt_monthdebt_lastmonthdebt_list {
                month_debt.starting_balance =
                    last_month_debt.map_or(debt.balance, |d| d.ending_balance);
                month_debt.interest_charge =
                    (month_debt.starting_balance * debt.interest_rate * Decimal::new(1, 2)
                        / Decimal::new(12, 0))
                    .round_dp(2);
                debt.payoff_interest += month_debt.interest_charge;

                month_debt.minimum_payment = std::cmp::min(
                    debt.monthly_payment,
                    month_debt.starting_balance + month_debt.interest_charge,
                );
                month_debt.extra_payment = Decimal::ZERO;
                month_debt.payoff_payment = Decimal::ZERO;
                month_debt.ending_balance = Decimal::ZERO;

                remaining_budget -= month_debt.minimum_payment;
            }

            //Month.Loan_StartingBalance = LastMonth.Loan_EndingBalance if LastMonth else 0
            month_plan.loan_starting_balance = if let Some(ref last) = last_month_plan {
                last.loan_ending_balance
            } else {
                Decimal::ZERO
            };

            /*
            for Debt, MonthDebt, LastMonthDebt in Debt_MonthDebt_LastMonthDebt_List:
                midmonthbalance = MonthDebt.StartingBalance + MonthDebt.InterestCharge - MonthDebt.MinimumPayment
                if remaining_cash_balance > midmonthbalance and midmonthbalance > 0:
                MonthDebt.PayoffPayment = midmonthbalance
                else:
                MonthDebt.PayoffPayment = 0

                remaining_cash_balance -= MonthDebt.PayoffPayment
                loan_used += MonthDebt.PayoffPayment
            */
            for (_debt, month_debt, _last_month_debt) in &mut debt_monthdebt_lastmonthdebt_list {
                let midmonthbalance = month_debt.starting_balance + month_debt.interest_charge
                    - month_debt.minimum_payment;
                if remaining_cash_balance > midmonthbalance && midmonthbalance > Decimal::ZERO {
                    month_debt.payoff_payment = midmonthbalance;
                } else {
                    month_debt.payoff_payment = Decimal::ZERO;
                }
                remaining_cash_balance -= month_debt.payoff_payment;
                loan_used += month_debt.payoff_payment;
            }

            //Month.Loan_Drawn = loan_used
            month_plan.loan_draw = loan_used;

            //# Third, pay all extra on insurance company loan
            /*
            Month.Loan_Paid = min(remaining_budget, Month.Loan_StartingBalance)
            remaining_budget -= Month.Loan_Paid
            */
            month_plan.loan_paid =
                std::cmp::min(remaining_budget, month_plan.loan_starting_balance);
            remaining_budget -= month_plan.loan_paid;

            //Month.Loan_EndingBalance = Month.Loan_StartingBalance + Month.Loan_Drawn - Month.Loan_Paid
            month_plan.loan_ending_balance =
                month_plan.loan_starting_balance + month_plan.loan_draw - month_plan.loan_paid;

            //# Fifth, if there is any left, pay extra on any loans remaining
            /*
            for Debt, MonthDebt, LastMonthDebt in Debt_MonthDebt_LastMonthDebt_List:
                MonthDebt.ExtraPayment = min(remaining_budget, MonthDebt.StartingBalance + MonthDebt.InterestCharge - MonthDebt.MinimumPayment - MonthDebt.PayoffPayment)
                remaining_budget -= MonthDebt.ExtraPayment
            */
            for (_debt, month_debt, _last_month_debt) in &mut debt_monthdebt_lastmonthdebt_list {
                month_debt.extra_payment = std::cmp::min(
                    remaining_budget,
                    month_debt.starting_balance + month_debt.interest_charge
                        - month_debt.minimum_payment
                        - month_debt.payoff_payment,
                );
                remaining_budget -= month_debt.extra_payment;
            }

            //# Fifth, calcualte final debt balances and when a debt is paid off
            /*
            for Debt, MonthDebt, LastMonthDebt in Debt_MonthDebt_LastMonthDebt_List:
                MonthDebt.EndingBalance = MonthDebt.StartingBalance + MonthDebt.InterestCharge - MonthDebt.MinimumPayment - MonthDebt.PayoffPayment - MonthDebt.ExtraPayment
                # Paid off first time?
                if MonthDebt.EndingBalance <= 0 and Debt.PayoffMonth is None:
                Debt.PayoffMonth = Month.Month
                Debt.PayoffDate = getdate(Debt.PayoffMonth)
            */
            for (debt, month_debt, _last_month_debt) in &mut debt_monthdebt_lastmonthdebt_list {
                month_debt.ending_balance = month_debt.starting_balance
                    + month_debt.interest_charge
                    - month_debt.minimum_payment
                    - month_debt.payoff_payment
                    - month_debt.extra_payment;
                if month_debt.ending_balance <= Decimal::ZERO && debt.payoff_month.is_none() {
                    debt.payoff_month = Some(month_plan.month);
                    debt.payoff_date = Some(get_date(month_plan.month));
                }
            }

            //# -- Get sums for month budget --
            /*
            Month.Budget_Total = RVAL.MonthlyBudget
            Month.Budget_Premium = Month.Premium_Total
            Month.Budget_MinimumPayments = sum(md.MinimumPayment for md in Month.DebtList)
            Month.Budget_ExtraPayments = sum(md.ExtraPayment for md in Month.DebtList)
            Month.Budget_Repayment = Month.Loan_Paid
            Month.Budget_Remaining = remaining_budget


            Month.Debt_StartingBalance = LastMonth.Debt_EndingBalance if LastMonth else sum(Debt.Balance for Debt in RVAL.DebtList)
            Month.Debt_InterestCharge = sum(md.InterestCharge for md in Month.DebtList)
            Month.Debt_PaidDown = sum(md.MinimumPayment + md.PayoffPayment + md.ExtraPayment for md in Month.DebtList)
            Month.Debt_EndingBalance = Month.Debt_StartingBalance + Month.Debt_InterestCharge - Month.Debt_PaidDown

            Month.Cash_Value = Month.PUA_Balance - Month.Loan_EndingBalance
            Month.Cash_Net = Month.Cash_Value - Month.Debt_EndingBalance
            */
            month_plan.budget_total = debt_info.monthly_budget.unwrap_or(Decimal::ZERO);
            month_plan.budget_premium = month_plan.premium_total;
            month_plan.budget_minimum_payments =
                month_plan.debt_list.iter().map(|d| d.minimum_payment).sum();
            month_plan.budget_extra_payments =
                month_plan.debt_list.iter().map(|d| d.extra_payment).sum();
            month_plan.budget_repayment = month_plan.loan_paid;
            month_plan.budget_remaining = remaining_budget;
            month_plan.debt_starting_balance = if let Some(last) = &last_month_plan {
                last.debt_ending_balance
            } else {
                debt_info.debt_list.iter().map(|d| d.balance).sum()
            };
            month_plan.debt_interest_charge =
                month_plan.debt_list.iter().map(|d| d.interest_charge).sum();
            month_plan.debt_paid_down = month_plan
                .debt_list
                .iter()
                .map(|d| d.minimum_payment + d.payoff_payment + d.extra_payment)
                .sum();
            month_plan.debt_ending_balance = month_plan.debt_starting_balance
                + month_plan.debt_interest_charge
                - month_plan.debt_paid_down;
            month_plan.cash_value = month_plan.pua_balance - month_plan.loan_ending_balance;
            month_plan.cash_net = month_plan.cash_value - month_plan.debt_ending_balance;

            //# SMS Analysis
            //Month.SMSMap = {}
            month_plan.sms_map = HashMap::new();

            //# Find out if any debts are paid off this month.
            /*
            payoff_name_list = [debt.FullName for debt in Month.DebtList if debt.PayoffPayment > 0]
            if payoff_name_list:
                Month.SMSMap['Payoff'] = f'''{self.Name} great news!
                It's time to pay off ''' + str.join(' and ', payoff_name_list) + f'''!
                Please call me at {self.AdvisorContact.Phone} to help.  {self.AdvisorContact.Name}'''
            */
            let payoff_name_list: Vec<&str> = month_plan
                .debt_list
                .iter()
                .filter_map(|debt| {
                    if debt.payoff_payment > Decimal::ZERO {
                        Some(debt.full_name.as_str())
                    } else {
                        None
                    }
                })
                .collect();
            if !payoff_name_list.is_empty() {
                month_plan.sms_map.insert(
                    "Payoff".to_string(),
                    format!(
                        "{} great news! It's time to pay off {}! Please call me at {} to help.  {}",
                        client.name,
                        payoff_name_list.join(" and "),
                        advisor.phone.as_ref().unwrap_or(&"".to_string()),
                        advisor.name
                    ),
                );
            }

            //# SMS: Snowball Shift
            //# last_moth_extra, this_month_extra, this_month_total_plus_extra are used for displaying the message, not for calculations.
            /*
            last_month_extra = [f'{debt.FullName}: {CUR0(round(debt.ExtraPayment))}' for debt in LastMonth.DebtList if round(debt.ExtraPayment)] if LastMonth else []
            this_month_extra = [f'{debt.FullName}: {CUR0(round(debt.ExtraPayment))}' for debt in Month.DebtList if round(debt.ExtraPayment)]

            this_month_total_plus_extra = round(sum(md.MinimumPayment + md.ExtraPayment for md in Month.DebtList))
            */
            let last_month_extra: Vec<String> = if let Some(last) = &last_month_plan {
                last.debt_list
                    .iter()
                    .filter_map(|debt| {
                        if debt.extra_payment > Decimal::ZERO {
                            Some(format!(
                                "{}: {}",
                                debt.full_name,
                                format_currency_us_0(debt.extra_payment)
                            ))
                        } else {
                            None
                        }
                    })
                    .collect()
            } else {
                Vec::new()
            };
            let this_month_extra: Vec<String> = month_plan
                .debt_list
                .iter()
                .filter_map(|debt| {
                    if debt.extra_payment > Decimal::ZERO {
                        Some(format!(
                            "{}: {}",
                            debt.full_name,
                            format_currency_us_0(debt.extra_payment)
                        ))
                    } else {
                        None
                    }
                })
                .collect();
            let this_month_total_plus_extra: Decimal = month_plan
                .debt_list
                .iter()
                .map(|debt| debt.minimum_payment + debt.extra_payment)
                .sum();

            /*
            if this_month_extra:
                # We need to check if last_month_extra is different than this_month_extra and we have value in this_month_extra. Unit#10078074.
                if last_month_extra != this_month_extra:
                    Month.SMSMap['ExtraShift'] = str(self.Name) + " it's time for a change ...
                    this month make these extra debt payments: 
                    " + (str(', '.join(this_month_extra))) + '. ' + str(self.AdvisorContact.Name) + ', ' + str(self.AdvisorContact.Phone)
                else:
                    Month.SMSMap['ContinueToPay'] = str(self.Name) + ' keep up the good work!
                    Continuing paying $' + str(Month.Budget_MinimumPayments) + " plus an extra for " + (str(', '.join(this_month_extra))) + '
                    to your debt snowball for a total payment of
                    $' + str(this_month_total_plus_extra)+ '. ' + str(self.AdvisorContact.Name) + ', ' + str(self.AdvisorContact.Phone)
             */
            if !this_month_extra.is_empty() {
                if last_month_extra != this_month_extra {
                    month_plan.sms_map.insert(
                        "ExtraShift".to_string(),
                        format!(
                            "{} it's time for a change ... this month make these extra debt payments: {} . {} , {}",
                            client.name,
                            this_month_extra.join(", "),
                            advisor.name,
                            advisor.phone.as_ref().unwrap_or(&"".to_string())
                        ),
                    );
                } else {
                    month_plan.sms_map.insert(
                        "ContinueToPay".to_string(),
                        format!(
                            "{} keep up the good work! Continuing paying {} plus an extra for {} to your debt snowball for a total payment of {}. {} , {}",
                            client.name,
                            format_currency_us_0(month_plan.budget_minimum_payments),
                            this_month_extra.join(", "),
                            format_currency_us_0(this_month_total_plus_extra),
                            advisor.name,
                            advisor.phone.as_ref().unwrap_or(&"".to_string())
                        ),
                    );
                }
            }

            // #SMS: to pay the remainder of your policy loan
            /*
            if not Month.Debt_EndingBalance and Month.Budget_Repayment:
                Month.SMSMap['ContinueToPay'] = str(self.Name) + " keep it up!
                Now it's time to pay off the remainder of your policy loan. 
                Continue paying $" + str(Month.Budget_Repayment) + " to pay off your policy loan."

            #Default message to send to the client only
            if not Month.SMSMap:
                Month.SMSMap['ClientReminder'] = f'''{self.Name} - Keep it up!!!
                Your DF4L plan is working.
                Every day you get one day closer to being Debt Free for Life.
                {self.AdvisorContact.Name}, {self.AdvisorContact.Phone}'''

            */

            if month_plan.debt_ending_balance <= Decimal::ZERO
                && month_plan.budget_repayment > Decimal::ZERO
            {
                month_plan.sms_map.insert(
                    "ContinueToPay".to_string(),
                    format!(
                        "{} keep it up! Now it's time to pay off the remainder of your policy loan. 
                        Continue paying {} to pay off your policy loan. ",
                        client.name,
                        format_currency_us_0(month_plan.budget_repayment),
                    ),
                );
            }
            if month_plan.sms_map.is_empty() {
                month_plan.sms_map.insert(
                    "ClientReminder".to_string(),
                    format!(
                        "{} - Keep it up!!! 
                        Your DF4L plan is working. 
                        Every day you get one day closer to being Debt Free for Life. 
                        {} , {}",
                        client.name,
                        advisor.name,
                        advisor.phone.as_ref().unwrap_or(&"".to_string())
                    ),
                );
            }

            /*
            current_date = datetime.date.today()

            #RunDate: datetime.date(2023, 11, 1)
            if RunDate:
                current_date = RunDate
            CurrentYear = current_date.year
            CurrentMonth = current_date.month
             */
            let current_date = run_date.unwrap_or_else(|| Local::now().date_naive());
            let current_year = current_date.year() as u32;
            let current_month = current_date.month();

            /*
            if Month.MonthAndYearTuple == (CurrentMonth,CurrentYear):
                payoff_info_list = [f"{debt.FullName} : ${debt.PayoffPayment:.2f}" for debt in Month.DebtList if debt.PayoffPayment > 0]
                RVAL.UpcomingChanges['CurrentMonth'] = {
                'StartingBalance' : Month.Debt_StartingBalance,
                'EndingBalance': Month.Debt_EndingBalance,
                'InterestCharge': Month.Debt_InterestCharge,
                'MinimumPayment': Month.Budget_MinimumPayments,
                'ThisMonthExtra': this_month_extra,
                'Payoff': payoff_info_list
                }
             */
            if month_plan.month_year_tuple.month == current_month
                && month_plan.month_year_tuple.year == current_year
            {
                let payoff_info_list: Vec<String> = month_plan
                    .debt_list
                    .iter()
                    .filter_map(|debt| {
                        if debt.payoff_payment > Decimal::ZERO {
                            Some(format!("{} : ${:.2}", debt.full_name, debt.payoff_payment))
                        } else {
                            None
                        }
                    })
                    .collect();

                debt_info.upcoming_changes.current_month = Some(MonthChange {
                    starting_balance: month_plan.debt_starting_balance,
                    ending_balance: month_plan.debt_ending_balance,
                    interest_charge: month_plan.debt_list.iter().map(|d| d.interest_charge).sum(),
                    minimum_payment: month_plan.debt_list.iter().map(|d| d.minimum_payment).sum(),
                    this_month_extra: this_month_extra.clone(),
                    payoff: payoff_info_list,
                });
            }

            /*
            nextmonth_date = current_date + relativedelta(months=1)
            NextMonthYear = nextmonth_date.year
            NextMonth = nextmonth_date.month
             */
            let next_month_date = add_months(current_date, 1);
            let next_month_year = next_month_date.year() as u32;
            let next_month = next_month_date.month();

            /*
            if Month.MonthAndYearTuple == (NextMonth,NextMonthYear):
                payoff_info_list = [f"{debt.FullName} : ${debt.PayoffPayment:.2f}" for debt in Month.DebtList if debt.PayoffPayment > 0]
                RVAL.UpcomingChanges['NextMonth'] = {
                'StartingBalance': Month.Debt_StartingBalance,
                'EndingBalance': Month.Debt_EndingBalance,
                'InterestCharge': Month.Debt_InterestCharge,
                'MinimumPayment': Month.Budget_MinimumPayments,
                'ThisMonthExtra': this_month_extra,
                'Payoff': payoff_info_list
                }
             */
            if month_plan.month_year_tuple.month == next_month
                && month_plan.month_year_tuple.year == next_month_year
            {
                let payoff_info_list: Vec<String> = month_plan
                    .debt_list
                    .iter()
                    .filter_map(|debt| {
                        if debt.payoff_payment > Decimal::ZERO {
                            Some(format!("{} : ${:.2}", debt.full_name, debt.payoff_payment))
                        } else {
                            None
                        }
                    })
                    .collect();

                debt_info.upcoming_changes.next_month = Some(MonthChange {
                    starting_balance: month_plan.debt_starting_balance,
                    ending_balance: month_plan.debt_ending_balance,
                    interest_charge: month_plan.debt_list.iter().map(|d| d.interest_charge).sum(),
                    minimum_payment: month_plan.debt_list.iter().map(|d| d.minimum_payment).sum(),
                    this_month_extra: this_month_extra.clone(),
                    payoff: payoff_info_list,
                });
            }

            /*
            followingmonth_date = current_date + relativedelta(months=2)
            FollowingMonthYear = followingmonth_date.year
            FollowingMonth = followingmonth_date.month
             */
            let following_month_date = add_months(current_date, 2);
            let following_month_year = following_month_date.year() as u32;
            let following_month = following_month_date.month();

            /*
            if Month.MonthAndYearTuple == (FollowingMonth,FollowingMonthYear):
                payoff_info_list = [f"{debt.FullName} : ${debt.PayoffPayment:.2f}" for debt in Month.DebtList if debt.PayoffPayment > 0]
                RVAL.UpcomingChanges['FollowingMonth'] = {
                'StartingBalance': Month.Debt_StartingBalance,
                'EndingBalance': Month.Debt_EndingBalance,
                'InterestCharge': Month.Debt_InterestCharge,
                'MinimumPayment': Month.Budget_MinimumPayments,
                'ThisMonthExtra': this_month_extra,
                'Payoff': payoff_info_list
                }
             */
            if month_plan.month_year_tuple.month == following_month
                && month_plan.month_year_tuple.year == following_month_year
            {
                let payoff_info_list: Vec<String> = month_plan
                    .debt_list
                    .iter()
                    .filter_map(|debt| {
                        if debt.payoff_payment > Decimal::ZERO {
                            Some(format!("{} : ${:.2}", debt.full_name, debt.payoff_payment))
                        } else {
                            None
                        }
                    })
                    .collect();

                debt_info.upcoming_changes.following_month = Some(MonthChange {
                    starting_balance: month_plan.debt_starting_balance,
                    ending_balance: month_plan.debt_ending_balance,
                    interest_charge: month_plan.debt_list.iter().map(|d| d.interest_charge).sum(),
                    minimum_payment: month_plan.debt_list.iter().map(|d| d.minimum_payment).sum(),
                    this_month_extra: this_month_extra.clone(),
                    payoff: payoff_info_list,
                });
            }
            //# Roll this month to LastMonth for comparisons in next iteration of loop
            //LastMonth = Month
            last_month_plan = Some(month_plan.clone());

            // Add month plan to output
            debt_info.plan.push(month_plan.clone());

            //# Bail out if we are at the end.
            //if Month.Debt_EndingBalance <=0 and Month.Loan_EndingBalance <= 0:
            //break
            if month_plan.debt_ending_balance <= Decimal::ZERO
                && month_plan.loan_ending_balance <= Decimal::ZERO
            {
                break;
            }
        } // end for loop

        // Calculate total paid and total unpaid amounts
        debt_info.total_paid = debt_info
            .plan
            .iter()
            .map(|m| m.premium_total + m.budget_total)
            .sum();
        debt_info.total_unpaid = debt_info.debt_balance.unwrap_or_default() - debt_info.total_paid;

        Ok(())
    }
}
