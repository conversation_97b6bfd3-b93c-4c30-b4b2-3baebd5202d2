pub mod api;
pub mod core;
pub mod web;

pub trait App:
    approck::App + approck_postgres::App + api_sendgrid::App + api_twilio::App + auth_fence::App
{
}

pub trait Identity: approck::Identity + rrr_zero::Identity + auth_fence::Identity {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }

    fn signup_agent_create(&self) -> bool {
        true
    }

    fn get_address(&self) -> String {
        auth_fence::Identity::remote_address(self).to_string()
    }

    #[allow(async_fn_in_trait)]
    async fn signup_agent_read(
        &self,
        dbcx: &impl approck_postgres::DB,
        signup_agent_uuid: granite::Uuid,
    ) -> bool;

    #[allow(async_fn_in_trait)]
    async fn signup_agent_write(
        &self,
        dbcx: &impl approck_postgres::DB,
        signup_agent_uuid: granite::Uuid,
    ) -> bool;
}

pub trait Document: bux::document::Base {}

pub fn ml_signup_agent(signup_agent_uuid: granite::Uuid) -> String {
    format!("/signup/agent/{}/", signup_agent_uuid)
}
