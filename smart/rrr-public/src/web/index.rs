#[approck::http(GET /; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("RRR");

        doc.add_body(html!(
            div."bux-narrow-50" {
                img.rrr-logo src="https://asset7.net/Zagula/Smart/RRR/3R-Updated-Logo.png" {}
                hr;
                (bux::button::link::label_icon_class("Login to Real Return Reporter™", "fas fa-sign-in-alt", "/auth/", "lg"))
            }
        ));

        Response::HTML(doc.into())
    }
}
