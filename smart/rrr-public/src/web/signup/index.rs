#[approck::http(GET|POST /signup/; AUTH None; return HTML|Redirect;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        req: Request,
        doc: Document,
    ) -> Result<Response> {
        use crate::api::signup::agent::signup_agent_create;
        use approck::html;

        if req.is_post() {
            let output = signup_agent_create::call(app, identity).await?;
            return Ok(Response::Redirect(Redirect::see_other(output.signup_url)));
        }

        doc.set_title("Get Started");

        doc.add_body(html!(
            form #signup method="post" {
                (bux::button::submit::label("Sign Up Now"))
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}
