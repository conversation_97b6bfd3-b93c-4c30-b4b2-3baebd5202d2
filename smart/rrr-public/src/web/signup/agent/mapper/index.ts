//-------------------------------------------------------------------------------------------------
// 1. Import Components

import "./index.mcss";
import "@bux/input/text/string.mts";
import "@addr-iso/input/address_us_select.mts";
import "@bux/component/form_wizard.mts";

// -------------------------------------------------------------------------------------------------
// 2. Import Code

import { SE, SEC } from "@granite/lib.mts";
import { signup_agent_contact_set } from "@crate/api/signup/agentλ.mts";
import BuxInputTextString from "@bux/input/text/string.mts";
import { AddressUsSelect } from "@addr-iso/input/address_us_select.mts";
import FormWizard from "@bux/component/form_wizard.mts";

// -------------------------------------------------------------------------------------------------
// 3. Find Elements

const $form = SE(document, "form.bux-form-wizard") as HTMLFormElement;

// Entire functionality of page conditional on this element existing.
if ($form.querySelector("content")) {
    const $signup_agent_uuid: HTMLInputElement = SE($form, "[name=signup_agent_uuid]");
    const $first_name: BuxInputTextString = SE($form, "[name=first_name]");
    const $last_name: BuxInputTextString = SE($form, "[name=last_name]");
    const $email: BuxInputTextString = SE($form, "[name=email]");
    const $phone: BuxInputTextString = SE($form, "[name=phone]");
    const $address1: BuxInputTextString = SE($form, "[name=address1]");
    const $address2: BuxInputTextString = SE($form, "[name=address2]");
    const $city: BuxInputTextString = SE($form, "[name=city]");
    const $state: AddressUsSelect = SE($form, "[name=state]");
    const $zip: BuxInputTextString = SE($form, "[name=zip]");
    const $country: BuxInputTextString = SE($form, "[name=country]");

    const signup_agent_uuid = $signup_agent_uuid.value;

    const $next_button = SEC(HTMLAnchorElement, $form, "a.x-next-button");

    // -------------------------------------------------------------------------------------------------
    // 4. Bind Event Handlers

    $next_button.addEventListener("click", (event) => {
        event.preventDefault();
        $form.dispatchEvent(new Event("submit"));
    });

    // -------------------------------------------------------------------------------------------------
    // 5. Write Code
    // -------------------------------------------------------------------------------------------------

    new FormWizard({
        $form,
        api: signup_agent_contact_set.api,

        err: (errors: any) => {
            $first_name.set_e(errors.first_name);
            $last_name.set_e(errors.last_name);
            $email.set_e(errors.email);
            $phone.set_e(errors.phone);
            $address1.set_e(errors.address1);
            $address2.set_e(errors.address2);
            $city.set_e(errors.city);
            $state.set_e(errors.state);
            $zip.set_e(errors.zip);
            $country.set_e(errors.country);
        },

        get: () => {
            return {
                signup_agent_uuid: signup_agent_uuid,
                first_name: $first_name.value,
                last_name: $last_name.value,
                email: $email.value,
                phone: $phone.value,
                address1: $address1.value,
                address2: $address2.value_option,
                city: $city.value,
                state: $state.value,
                zip: $zip.value,
                country: $country.value,
            };
        },

        set: (_value) => {
        },

        out: (_output) => {
            window.location.href = $next_button.href;
        },
    });
}
