#[approck::http(GET /signup/agent/{signup_agent_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        doc: Document,
        identity: Identity,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use crate::api::signup::agent::{signup_agent_contact_get, signup_agent_wizard_data};
        use approck::html;

        doc.add_css("./index.mcss");
        doc.add_js("./index.ts");

        let wizard_data = signup_agent_wizard_data::call(
            app,
            identity,
            signup_agent_wizard_data::Input {
                signup_agent_uuid: path.signup_agent_uuid,
            },
        )
        .await?;

        let personal_data = signup_agent_contact_get::call(
            app,
            identity,
            signup_agent_contact_get::Input {
                signup_agent_uuid: path.signup_agent_uuid,
            },
        )
        .await?;

        let mut wizard = bux::component::form_wizard::new(WizardStep::Index, wizard_data)?;
        wizard.set_id("personal-info-form");
        wizard.set_hidden("action", "signup");
        wizard.set_hidden("signup_agent_uuid", path.signup_agent_uuid);

        #[rustfmt::skip]
        wizard.add_body(html! {
            p {"Please fill out your personal details. All fields are required to continue."} br;
            grid-2 {
                div {
                    h4 { "Contact Information" }
                    (bux::input::text::string::name_label_value("first_name", "First Name:", personal_data.first_name.as_deref()))
                    (bux::input::text::string::name_label_value("last_name", "Last Name:", personal_data.last_name.as_deref()))
                    div {
                        (bux::input::text::string::name_label_value("email", "Primary Email:", personal_data.email.as_deref()))
                        p { "A verification code will be sent to this email address." }
                    }
                    div {
                        (bux::input::text::string::name_label_value("phone", "Mobile Phone:", personal_data.phone.as_deref()))
                        p { "We'll send SMS updates to this mobile number." }
                    }
                }
                div {
                    h4 { "Address Information" }
                    (bux::input::text::string::name_label_value("address1", "Address Line 1:", personal_data.address1.as_deref()))
                    (bux::input::text::string::name_label_value("address2", "Address Line 2:", personal_data.address2.as_deref()))
                    (bux::input::text::string::name_label_value("city", "City:", personal_data.city.as_deref()))
                    (addr_iso::input::address_us_select::us_state_select_with_help(app, "state", "State", personal_data.state.as_deref(), "").await?)
                    (bux::input::text::string::name_label_value("zip", "ZIP Code:", personal_data.zip.as_deref()))
                    (bux::input::text::string::name_label_value("country", "Country:", personal_data.country.as_deref().or(Some("United States"))))
                }
            }
        });

        doc.set_title("Personal Information");
        doc.add_body(html! {
            (wizard)
        });

        Ok(Response::HTML(doc.into()))
    }
}
