//-------------------------------------------------------------------------------------------------
// 1. Import Components
import "./terms.mcss";
import "@bux/component/form_wizard.mts";
import "@bux/input/text/string.mts";

// -------------------------------------------------------------------------------------------------
// 2. Import Code
import { SE, SEC } from "@granite/lib.mts";
import { signup_agent_terms_set } from "@crate/api/signup/agentλ.mts";
import BuxInputTextString from "@bux/input/text/string.mjs";
import FormWizard from "@bux/component/form_wizard.mts";

// -------------------------------------------------------------------------------------------------
// 3. Find Elements
const $form = SE(document, "form.bux-form-wizard") as HTMLFormElement;

// Entire functionality of page conditional on this element existing.
if ($form.querySelector("content")) {
    const signup_agent_uuid =
        SE<HTMLInputElement>($form, "input[type=hidden][name=signup_agent_uuid]").value;
    const document_uuid =
        SE<HTMLInputElement>($form, "input[type=hidden][name=document_uuid]").value;
    const revision = SE<HTMLInputElement>($form, "input[type=hidden][name=revision]").value;
    const $terms_accepted_name = BuxInputTextString.SEC_name($form, "terms_accepted_name");
    const $x_sign_error = SE($form, ".x-sign > .x-error") as HTMLDivElement;

    // continue button
    const $next_button = SEC(HTMLAnchorElement, $form, "a.x-next-button");

    // -------------------------------------------------------------------------------------------------
    // 4. Bind Event Handlers

    $next_button.addEventListener("click", (event) => {
        event.preventDefault();
        $form.dispatchEvent(new Event("submit"));
    });

    // -------------------------------------------------------------------------------------------------
    // 5. Write Code
    // -------------------------------------------------------------------------------------------------

    new FormWizard({
        $form,
        api: signup_agent_terms_set.api,

        err: (errors) => {
            $x_sign_error.textContent = errors.terms_accepted_name ?? null;

            $terms_accepted_name.set_e(errors.terms_accepted_name);
        },

        get: () => {
            $x_sign_error.textContent = "";

            return {
                signup_agent_uuid: signup_agent_uuid,
                document_uuid: document_uuid,
                revision: revision,
                terms_accepted_name: $terms_accepted_name.value,
            };
        },

        set: (_value) => {
        },

        out: (_output) => {
            window.location.href = $next_button.href;
        },
    });
}
