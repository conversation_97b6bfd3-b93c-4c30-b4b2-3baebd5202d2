pub mod index;
pub mod signup;

#[approck::prefix(/)]
pub mod prefix {
    pub fn menu(menu: <PERSON>u, identity: Identity) {
        menu.set_order(-100);
        menu.add_link("Home", "/");

        if identity.is_logged_in() {
            menu.add_link("Dashboard", "/dashboard");
        } else {
            menu.add_link("Get Started", "/signup/");
            menu.add_link("Sign In", "/auth/");
        }
    }
}
