impl rrr_public::App for crate::AppStruct {}

impl rrr_public::Identity for crate::IdentityStruct {
    async fn signup_agent_read(
        &self,
        dbcx: &impl approck_postgres::DB,
        signup_agent_uuid: granite::Uuid,
    ) -> bool {
        self.signup_agent_write(dbcx, signup_agent_uuid).await
    }

    async fn signup_agent_write(
        &self,
        dbcx: &impl approck_postgres::DB,
        signup_agent_uuid: granite::Uuid,
    ) -> bool {
        let session_token = &self.session_token;

        // if the session token matches the signup_agent_uuid, allow access
        let result = granite::pg_row_option!(
            db = dbcx;
            args = {
                $session_token: &session_token,
                $signup_agent_uuid: &signup_agent_uuid,
            };
            row = {
                exists: bool,
            };
            SELECT
                true as exists
            FROM
                rrr.signup_agent
            WHERE
                signup_agent_uuid = $signup_agent_uuid::uuid
                AND session_token = $session_token::varchar
        )
        .await;

        match result {
            Ok(Some(_row)) => true,
            Ok(None) => false,
            Err(e) => {
                approck::error!("Error checking signup_agent_read permission: {:?}", e);
                false
            }
        }
    }
}
