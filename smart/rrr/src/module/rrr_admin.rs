impl rrr_admin::App for crate::AppStruct {}

impl rrr_admin::Identity for crate::IdentityStruct {
    fn web_usage(&self) -> bool {
        if self.rrr_admin.is_some() {
            return true;
        }

        false
    }

    fn api_usage(&self) -> bool {
        self.web_usage()
    }

    fn agent_list(&self) -> bool {
        match &self.rrr_admin {
            Some(rrr_admin) => rrr_admin.agent_read,
            None => false,
        }
    }
    fn agent_add(&self) -> bool {
        match &self.rrr_admin {
            Some(rrr_admin) => rrr_admin.agent_write,
            None => false,
        }
    }
    fn agent_read(&self, _agent_uuid: granite::Uuid) -> bool {
        match &self.rrr_admin {
            Some(rrr_admin) => rrr_admin.agent_read,
            None => false,
        }
    }
    fn agent_write(&self, _agent_uuid: granite::Uuid) -> bool {
        match &self.rrr_admin {
            Some(rrr_admin) => rrr_admin.agent_write,
            None => false,
        }
    }
}
