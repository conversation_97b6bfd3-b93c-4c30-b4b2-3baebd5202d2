:root {

    /* split button */
    --cliffy-splitbtn-a-bg-color: #6d738c;
    --cliffy-splitbtn-a-border-color: #6d738c;
    --cliffy-splitbtn-btn-bg-color: #4c5162;
    --cliffy-splitbtn-btn-border-color: #4c5162;

    /* navigation */
    --bux-nav-a-border: none;
    --bux-nav-a-br: 0.375rem;
    --bux-nav-a-color: #000;
    --bux-nav-a-hover-color: #000;
    --bux-nav-a-bg-color: rgb(172,137,80); /* Fallback */
    --bux-nav-a-bg-image: linear-gradient(0deg, rgba(172,137,80,1) 0%, rgba(189,161,115,1) 50%, rgba(205,184,150,1) 100%);
    --bux-nav-a-hover-bg-color: rgb(172,137,80); /* Fallback */
    --bux-nav-a-hover-bg-image: linear-gradient(0deg, rgba(172,137,80,1) 0%, rgba(189,161,115,1) 50%, rgba(205,184,150,1) 100%);
    --bux-nav-wrap-bg-color: #11294f;
    
    /* cliffy */
    --cliffy-pnav-bg-color: #11294f;
    --cliffy-pnav-logo-width: 175px;
    --cliffy-pnav-menu-item-margin: 0 0 1rem 0;
    --cliffy-pnav-a-color: #fff;
    --cliffy-pnav-a-hover-color: #fff;
    --cliffy-pnav-a-weight: 500;
    --cliffy-pnav-a-lh: 1.25;
    --cliffy-pnav-a-border: 1px solid #666C71;
    --cliffy-pnav-a-bg-color: rgb(17,41,79); /* Fallback */
    --cliffy-pnav-a-bg-image: linear-gradient(0deg, rgba(17,41,79,1) 0%, rgba(65,84,114,1) 50%, rgba(112,127,149,1) 100%);
    --cliffy-pnav-a-hover-bg-color: rgb(17,41,79); /* Fallback */
    --cliffy-pnav-a-hover-bg-image: linear-gradient(0deg, rgba(17,41,79,1) 0%, rgba(65,84,114,1) 50%, rgba(112,127,149,1) 100%);

    /* hierarchical navigation */
    --pnav-a-selected-color: #fff;
    --pnav-group-name-top-border: #6d738c;
    --pnav-group-name-hover-top-border: #cdb896;
    --pnav-group-name-selected-top-border: #6d738c;
    --pnav-group-name-bottom-border: #6d738c;
    --pnav-group-name-hover-bottom-border: #ac8950;
    --pnav-group-name-selected-bottom-border: #6d738c;
    --pnav-a-hover-bg-color: rgb(172,137,80); /* Fallback */
    --pnav-a-hover-bg-image: linear-gradient(0deg, rgba(172,137,80,1) 0%, rgba(189,161,115,1) 50%, rgba(205,184,150,1) 100%);
    --pnav-a-selected-bg-color: #6d738c;

    /* page navigation */
    --bux-page-nav-a-selected-bg-color: #6D738C;
    --bux-page-nav-a-selected-border-color: #6D738C;

}

/*********************************************************/
/* Styles specific to the secondary navigation */

nav#secondary-navigation {

    ul.nav-menu {

        li.menu-item-dropdown {

            &#user-menu {

                a.dropdown-menu-toggle {

                    img {
                        display: none;
                    }
                }
            }
        }

        @media (min-width: 992px) {
            display: flex;
            flex-direction: row;
            align-items: center;
            column-gap: .5rem;
        }
    }
}

/*********************************************************/